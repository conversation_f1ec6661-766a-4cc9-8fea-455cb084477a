"use client";

import { UserCheck, Calendar, Users, BookOpen, Filter, Plus, Edit, Trash2 } from "lucide-react";
import SchoolLayout from "@/components/Dashboard/Layouts/SchoolLayout";
import { Suspense, useEffect, useState } from "react";
import DataTableFix from "@/components/utils/TableFix";

import CircularLoader from "@/components/widgets/CircularLoader";
import useAuth from "@/app/hooks/useAuth";
import ProtectedRoute from "@/components/utils/ProtectedRoute";
import { motion } from "framer-motion";
import { SchoolAdminTeacherAssignmentSkeleton } from "@/components/skeletons";
import {
  getTeacherAssignmentsBySchool,
  createTeacherAssignment,
  updateTeacherAssignment,
  deleteTeacherAssignment,
  getAvailableTeachers,
  TeacherAssignment
} from "@/app/services/TeacherAssignmentServices";
import { getClassesBySchool } from "@/app/services/ClassServices";
import { getSubjectsBySchoolId } from "@/app/services/SubjectServices";
import { verifyPassword } from "@/app/services/UserServices";
import NewTeacherAssignmentModal from "@/components/modals/NewTeacherAssignmentModal";
import PasswordConfirmDeleteModal from "@/components/modals/PasswordConfirmDeleteModal";
import { useToast, ToastContainer } from "@/components/ui/Toast";

const navigation = {
  icon: UserCheck,
  baseHref: "/school-admin/teacher-assignment",
  title: "Teacher Assignment"
};

export default function TeacherAssignmentPage() {
  const { logout, user } = useAuth();
  const { toasts, removeToast, showSuccess, showError } = useToast();

  // State management
  const [assignments, setAssignments] = useState<TeacherAssignment[]>([]);
  const [loadingData, setLoadingData] = useState(true);
  const [selectedClass, setSelectedClass] = useState('all');
  const [selectedTeacher, setSelectedTeacher] = useState('all');
  const [selectedAcademicYear, setSelectedAcademicYear] = useState('all');

  // Modal states
  const [isAssignmentModalOpen, setIsAssignmentModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [assignmentToEdit, setAssignmentToEdit] = useState<TeacherAssignment | null>(null);
  const [assignmentToDelete, setAssignmentToDelete] = useState<TeacherAssignment | null>(null);
  const [deleteType, setDeleteType] = useState<"single" | "multiple">("single");
  const [selectedAssignments, setSelectedAssignments] = useState<TeacherAssignment[]>([]);

  // Additional data for forms
  const [classes, setClasses] = useState<any[]>([]);
  const [subjects, setSubjects] = useState<any[]>([]);
  const [teachers, setTeachers] = useState<any[]>([]);

  // Loading states
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [clearSelection, setClearSelection] = useState(false);

  // Get school ID from user
  const schoolId = user?.school_ids?.[0] || user?.school_id;

  // Fetch assignment data from API
  useEffect(() => {
    const fetchAssignmentData = async () => {
      if (!schoolId) {
        showError("Error", "No school ID found");
        setLoadingData(false);
        return;
      }

      try {
        setLoadingData(true);

        // Build filters
        const filters: any = {};
        if (selectedClass !== 'all') filters.class_id = selectedClass;
        if (selectedTeacher !== 'all') filters.teacher_id = selectedTeacher;
        if (selectedAcademicYear !== 'all') filters.academic_year = selectedAcademicYear;

        // Fetch teacher assignments
        const response = await getTeacherAssignmentsBySchool(schoolId as string, filters);
        setAssignments(response.assignments);
      } catch (error) {
        console.error("Error fetching assignment data:", error);
        showError("Error", "Failed to load assignment data");
      } finally {
        setLoadingData(false);
      }
    };

    fetchAssignmentData();
  }, [schoolId, selectedClass, selectedTeacher, selectedAcademicYear]);

  // Fetch additional data for modals
  useEffect(() => {
    const fetchAdditionalData = async () => {
      if (!schoolId) return;

      try {
        // Fetch data with individual error handling
        const results = await Promise.allSettled([
          getClassesBySchool(schoolId as string),
          getSubjectsBySchoolId(schoolId as string),
          getAvailableTeachers(schoolId as string)
        ]);

        // Handle each result individually
        if (results[0].status === 'fulfilled') {
          setClasses(results[0].value);
        } else {
          console.error("Failed to fetch classes:", results[0].reason);
          setClasses([]);
        }

        if (results[1].status === 'fulfilled') {
          setSubjects(results[1].value);
        } else {
          console.error("Failed to fetch subjects:", results[1].reason);
          setSubjects([]);
        }

        if (results[2].status === 'fulfilled') {
          setTeachers(results[2].value);
        } else {
          console.error("Failed to fetch teachers:", results[2].reason);
          setTeachers([]);
        }

        // Show warning if any critical data failed to load
        const anyDataFailed = results.some(result => result.status === 'rejected');
        if (anyDataFailed) {
          showError("Warning", "Some form data could not be loaded. Some features may be limited.");
        }
      } catch (error) {
        console.error("Error fetching additional data:", error);
        showError("Error", "Failed to load form data");
      }
    };

    fetchAdditionalData();
  }, [schoolId]);

  // CRUD Functions
  const handleCreateAssignment = () => {
    setAssignmentToEdit(null);
    setIsAssignmentModalOpen(true);
  };

  const handleEditAssignment = (assignment: TeacherAssignment) => {
    setAssignmentToEdit(assignment);
    setIsAssignmentModalOpen(true);
  };

  const handleDeleteAssignment = (assignment: TeacherAssignment) => {
    setAssignmentToDelete(assignment);
    setDeleteType("single");
    setIsDeleteModalOpen(true);
  };

  const handleDeleteMultiple = () => {
    setDeleteType("multiple");
    setIsDeleteModalOpen(true);
  };

  const handleSelectionChange = (selectedRows: TeacherAssignment[]) => {
    setSelectedAssignments(selectedRows);
  };

  const handleDeleteConfirm = async (password: string) => {
    if (!schoolId || !user) return;

    try {
      // Verify password
      const passwordVerified = await verifyPassword(password, user.email);
      if (!passwordVerified) {
        showError("Error", "Invalid password. Please try again.");
        throw new Error("Invalid password");
      }

      setIsSubmitting(true);

      if (deleteType === "single" && assignmentToDelete) {
        // Delete single assignment
        await deleteTeacherAssignment(assignmentToDelete._id);
        showSuccess("Assignment Deleted", "Teacher assignment has been deleted successfully.");
      } else if (deleteType === "multiple") {
        // Delete multiple assignments
        await Promise.all(
          selectedAssignments.map(assignment =>
            deleteTeacherAssignment(assignment._id)
          )
        );
        showSuccess("Assignments Deleted", `${selectedAssignments.length} teacher assignments have been deleted successfully.`);
        setClearSelection(true);
      }

      // Close modal and reset state
      setIsDeleteModalOpen(false);
      setAssignmentToDelete(null);
      setSelectedAssignments([]);

      // Refresh assignments list
      const filters: any = {};
      if (selectedClass !== 'all') filters.class_id = selectedClass;
      if (selectedTeacher !== 'all') filters.teacher_id = selectedTeacher;
      if (selectedAcademicYear !== 'all') filters.academic_year = selectedAcademicYear;
      const response = await getTeacherAssignmentsBySchool(schoolId as string, filters);
      setAssignments(response.assignments);

    } catch (error: any) {
      console.error("Error deleting assignment(s):", error);
      if (error.message !== "Invalid password") {
        showError("Error", error.message || "Failed to delete assignment(s). Please try again.");
      }
      throw error;
    } finally {
      setIsSubmitting(false);
    }
  };

  // Modal submission function
  const handleAssignmentSubmit = async (data: any) => {
    if (!schoolId) {
      showError("Error", "No school ID found");
      return;
    }

    setIsSubmitting(true);
    try {
      if (assignmentToEdit) {
        // Update existing assignment
        await updateTeacherAssignment(assignmentToEdit._id, {
          subjects: data.subjects,
          academic_year: data.academic_year
        });
        showSuccess("Success", "Assignment updated successfully");
      } else {
        // Create new assignment
        await createTeacherAssignment(schoolId as string, data);
        showSuccess("Success", "Assignment created successfully");
      }

      // Refresh assignments list
      const filters: any = {};
      if (selectedClass !== 'all') filters.class_id = selectedClass;
      if (selectedTeacher !== 'all') filters.teacher_id = selectedTeacher;
      if (selectedAcademicYear !== 'all') filters.academic_year = selectedAcademicYear;

      const response = await getTeacherAssignmentsBySchool(schoolId as string, filters);
      setAssignments(response.assignments);
      setIsAssignmentModalOpen(false);
      setAssignmentToEdit(null);
    } catch (error: any) {
      console.error("Error submitting assignment:", error);
      showError("Error", error.message || "Failed to save assignment. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };



  // Table columns
  const columns = [
    {
      header: "Teacher",
      accessor: (row: TeacherAssignment) => {
        const teacherName = row.teacher_id.first_name && row.teacher_id.last_name
          ? `${row.teacher_id.first_name} ${row.teacher_id.last_name}`
          : row.teacher_id.name;
        return (
          <div>
            <p className="font-medium text-foreground">{teacherName}</p>
            <p className="text-sm text-gray-500">{row.teacher_id.email}</p>
          </div>
        );
      },
      searchValue: (row: TeacherAssignment) => {
        const teacherName = row.teacher_id.first_name && row.teacher_id.last_name
          ? `${row.teacher_id.first_name} ${row.teacher_id.last_name}`
          : row.teacher_id.name;
        return `${teacherName} ${row.teacher_id.email}`;
      }
    },
    {
      header: "Class",
      accessor: (row: TeacherAssignment) => (
        <div>
          <span className="text-sm font-medium">{row.class_id.name}</span>
          {row.class_id.class_code && (
            <span className="text-xs text-gray-500 ml-2">({row.class_id.class_code})</span>
          )}
        </div>
      ),
      searchValue: (row: TeacherAssignment) => `${row.class_id.name} ${row.class_id.class_code || ''}`
    },
    {
      header: "Subjects",
      accessor: (row: TeacherAssignment) => (
        <div className="flex flex-wrap gap-1">
          {row.subjects.map((subject, index) => (
            <span
              key={index}
              className="px-2 py-1 bg-teal/10 text-teal text-xs rounded-full"
            >
              {subject}
            </span>
          ))}
        </div>
      ),
      searchValue: (row: TeacherAssignment) => row.subjects.join(' ')
    },
    {
      header: "Academic Year",
      accessor: (row: TeacherAssignment) => (
        <span className="text-sm">{row.academic_year}</span>
      ),
      searchValue: (row: TeacherAssignment) => row.academic_year
    },

  ];

  // Actions for the table
  const actions = [
    {
      label: "Edit",
      onClick: (assignment: TeacherAssignment) => {
        handleEditAssignment(assignment);
      },
    },
    {
      label: "Delete",
      onClick: (assignment: TeacherAssignment) => {
        handleDeleteAssignment(assignment);
      },
    },
  ];

  // Filter data based on selections
  const filteredAssignments = assignments.filter(assignment => {
    if (selectedClass !== 'all') {
      if (assignment.class_id._id !== selectedClass) return false;
    }
    if (selectedTeacher !== 'all') {
      if (assignment.teacher_id._id !== selectedTeacher) return false;
    }
    if (selectedAcademicYear !== 'all' && assignment.academic_year !== selectedAcademicYear) return false;
    return true;
  });

  if (loadingData) {
    return (
      <ProtectedRoute allowedRoles={["school_admin", "admin", "super"]}>
        <SchoolLayout navigation={navigation} onLogout={logout}>
          <SchoolAdminTeacherAssignmentSkeleton />
        </SchoolLayout>
      </ProtectedRoute>
    );
  }

  return (
      <SchoolLayout navigation={navigation} onLogout={logout}>
        <div className="space-y-6">
          {/* Header */}
          <div className="bg-widget rounded-lg border border-stroke p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-teal rounded-full flex items-center justify-center">
                  <UserCheck className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-foreground">Teacher Assignment</h1>
                  <p className="text-foreground/60">
                    Assign teachers to classes and subjects for the academic year
                  </p>
                </div>
              </div>
              
              <div className="text-right">
                <p className="text-2xl font-bold text-foreground">{assignments.length}</p>
                <p className="text-sm text-foreground/60">Total Assignments</p>
              </div>
            </div>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-widget rounded-lg border border-stroke p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-foreground/60">Total Teachers</p>
                  <p className="text-2xl font-bold text-foreground">{teachers.length}</p>
                </div>
                <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                  <Users className="h-5 w-5 text-white" />
                </div>
              </div>
            </div>

            <div className="bg-widget rounded-lg border border-stroke p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-foreground/60">Total Classes</p>
                  <p className="text-2xl font-bold text-foreground">{classes.length}</p>
                </div>
                <div className="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center">
                  <BookOpen className="h-5 w-5 text-white" />
                </div>
              </div>
            </div>

            <div className="bg-widget rounded-lg border border-stroke p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-foreground/60">Active Assignments</p>
                  <p className="text-2xl font-bold text-foreground">{assignments.length}</p>
                </div>
                <div className="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center">
                  <UserCheck className="h-5 w-5 text-white" />
                </div>
              </div>
            </div>

            <div className="bg-widget rounded-lg border border-stroke p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-foreground/60">Subjects</p>
                  <p className="text-2xl font-bold text-foreground">{subjects.length}</p>
                </div>
                <div className="w-10 h-10 bg-orange-500 rounded-lg flex items-center justify-center">
                  <BookOpen className="h-5 w-5 text-white" />
                </div>
              </div>
            </div>
          </div>

          {/* Filters */}
          <div className="bg-widget rounded-lg border border-stroke p-6">
            <div className="flex flex-wrap items-center gap-4">
              <div className="flex items-center space-x-2">
                <Filter className="h-4 w-4 text-foreground/60" />
                <span className="text-sm font-medium text-foreground">Filters:</span>
              </div>

              <div className="flex items-center space-x-2">
                <label className="text-sm text-foreground/70">Class:</label>
                <select
                  value={selectedClass}
                  onChange={(e) => setSelectedClass(e.target.value)}
                  className="px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground"
                >
                  <option value="all">All Classes</option>
                  {classes.map((classItem) => (
                    <option key={classItem._id} value={classItem._id}>
                      {classItem.name}
                    </option>
                  ))}
                </select>
              </div>

              <div className="flex items-center space-x-2">
                <label className="text-sm text-foreground/70">Teacher:</label>
                <select
                  value={selectedTeacher}
                  onChange={(e) => setSelectedTeacher(e.target.value)}
                  className="px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground"
                >
                  <option value="all">All Teachers</option>
                  {teachers.map((teacher) => (
                    <option key={teacher._id} value={teacher._id}>
                      {teacher.first_name && teacher.last_name ?
                        `${teacher.first_name} ${teacher.last_name}` :
                        teacher.name || 'Unknown Teacher'}
                    </option>
                  ))}
                </select>
              </div>

              <div className="flex items-center space-x-2">
                <label className="text-sm text-foreground/70">Academic Year:</label>
                <select
                  value={selectedAcademicYear}
                  onChange={(e) => setSelectedAcademicYear(e.target.value)}
                  className="px-3 py-1 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground"
                >
                  <option value="all">All Years</option>
                  <option value="2024-2025">2024-2025</option>
                  <option value="2023-2024">2023-2024</option>
                  <option value="2025-2026">2025-2026</option>
                </select>
              </div>
            </div>
          </div>

          {/* Assignments Table */}
          <div className="bg-widget rounded-lg border border-stroke p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-semibold text-foreground">
                Teacher Assignments ({filteredAssignments.length})
              </h2>

              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleCreateAssignment}
                className="flex items-center space-x-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600"
              >
                <Plus size={16} />
                <span>New Assignment</span>
              </motion.button>
            </div>

            <Suspense fallback={<CircularLoader size={24} color="teal" />}>
              <DataTableFix<TeacherAssignment>
                data={filteredAssignments}
                columns={columns}
                actions={actions}
                defaultItemsPerPage={15}
                onSelectionChange={handleSelectionChange}
                handleDeleteMultiple={handleDeleteMultiple}
                clearSelection={clearSelection}
                onSelectionCleared={() => setClearSelection(false)}
              />
            </Suspense>
          </div>
        </div>

        {/* Teacher Assignment Modal */}
        <NewTeacherAssignmentModal
          isOpen={isAssignmentModalOpen}
          onClose={() => {
            setIsAssignmentModalOpen(false);
            setAssignmentToEdit(null);
          }}
          onSubmit={handleAssignmentSubmit}
          assignment={assignmentToEdit}
          classes={classes}
          subjects={subjects}
          teachers={teachers}
          loading={isSubmitting}
        />

        {/* Delete Confirmation Modal */}
        <PasswordConfirmDeleteModal
          isOpen={isDeleteModalOpen}
          onClose={() => {
            setIsDeleteModalOpen(false);
            setAssignmentToDelete(null);
          }}
          onConfirm={handleDeleteConfirm}
          title={
            deleteType === "single"
              ? "Delete Teacher Assignment"
              : "Delete Selected Assignments"
          }
          message={
            deleteType === "single"
              ? "Are you sure you want to delete this teacher assignment? This action cannot be undone."
              : `Are you sure you want to delete ${selectedAssignments.length} selected assignments? This action cannot be undone.`
          }
          itemName={
            deleteType === "single" && assignmentToDelete
              ? `${assignmentToDelete.teacher_name} - ${assignmentToDelete.class_name} (${assignmentToDelete.subject_name})`
              : undefined
          }
          itemCount={deleteType === "multiple" ? selectedAssignments.length : undefined}
          type={deleteType}
        />

        {/* Toast Notifications */}
        <ToastContainer toasts={toasts} onClose={removeToast} />
      </SchoolLayout>
  );
}
