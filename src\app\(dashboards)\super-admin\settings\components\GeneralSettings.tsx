"use client";

import React from "react";
import { Check } from "lucide-react";
import { Input } from "@/components/ui/Input";
import { Button } from "@/components/ui/Button";
import { Switch } from "@/components/ui/Switch"; // Ensure this is available or create one

interface GeneralSettingsForm {
  platform_name: string;
  support_email: string;
  default_language: string;
  maintenance_mode: boolean;
  maintenance_message?: string;
}

interface GeneralSettingsProps {
  generalForm: GeneralSettingsForm;
  setGeneralForm: React.Dispatch<React.SetStateAction<GeneralSettingsForm>>;
  handleGeneralSubmit: () => Promise<void>;
}

const GeneralSettings: React.FC<GeneralSettingsProps> = ({
  generalForm,
  setGeneralForm,
  handleGeneralSubmit,
}) => {
  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-semibold text-foreground mb-4">General Settings</h2>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Platform Name */}
        <div>
          <label htmlFor="platform-name-input" className="block text-sm font-medium text-foreground mb-1">
            Platform Name
          </label>
          <Input
            id="platform-name-input"
            type="text"
            value={generalForm.platform_name}
            onChange={(e: { target: { value: any; }; }) =>
              setGeneralForm({ ...generalForm, platform_name: e.target.value })
            }
            className="bg-background border-gray-300 rounded-md focus:ring-teal focus:border-teal"
            placeholder="Your Platform Name"
          />
        </div>

        {/* Support Email */}
        <div>
          <label htmlFor="support-email-input" className="block text-sm font-medium text-foreground mb-1">
            Support Email
          </label>
          <Input
            id="support-email-input"
            type="email"
            value={generalForm.support_email}
            onChange={(e: { target: { value: any; }; }) =>
              setGeneralForm({ ...generalForm, support_email: e.target.value })
            }
            className="bg-background border-gray-300 rounded-md focus:ring-teal focus:border-teal"
            placeholder="Support Email"
          />
        </div>

        {/* Default Language */}
        <div>
          <label htmlFor="default-language-input" className="block text-sm font-medium text-foreground mb-1">
            Default Language
          </label>
          <Input
            id="default-language-input"
            type="text"
            value={generalForm.default_language}
            onChange={(e: { target: { value: any; }; }) =>
              setGeneralForm({ ...generalForm, default_language: e.target.value })
            }
            className="bg-background border-gray-300 rounded-md focus:ring-teal focus:border-teal"
            placeholder="e.g. en, fr, es"
          />
        </div>

        {/* Maintenance Mode */}
        <div className="flex items-center space-x-4 mt-6">
          <Switch
            checked={generalForm.maintenance_mode}
            onCheckedChange={(checked) =>
              setGeneralForm({ ...generalForm, maintenance_mode: checked })
            }
            id="maintenance-mode-toggle"
          />
          <label htmlFor="maintenance-mode-toggle" className="text-sm font-medium text-foreground">
            Maintenance Mode
          </label>
        </div>

        {/* Maintenance Message (conditionally rendered) */}
        {generalForm.maintenance_mode && (
          <div className="md:col-span-2">
            <label htmlFor="maintenance-message-input" className="block text-sm font-medium text-foreground mb-1">
              Maintenance Message
            </label>
            <Input
              id="maintenance-message-input"
              type="text"
              value={generalForm.maintenance_message || ""}
              onChange={(e: { target: { value: any; }; }) =>
                setGeneralForm({ ...generalForm, maintenance_message: e.target.value })
              }
              className="border-gray-300 rounded-md focus:ring-teal focus:border-teal"
              placeholder="We'll be back soon!"
            />
          </div>
        )}
      </div>

      <Button
        onClick={handleGeneralSubmit}
        className="w-full py-3 bg-teal hover:opacity-90 text-white font-semibold rounded-md shadow-sm transition-colors duration-200 flex items-center justify-center space-x-2"
      >
        <Check className="h-5 w-5" />
        <span>Update General Settings</span>
      </Button>
    </div>
  );
};

export default GeneralSettings;
