"use client";

import React, { Suspense, useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, BookO<PERSON> } from "lucide-react";
import TeacherLayout from "@/components/Dashboard/Layouts/TeacherLayout";
import CircularLoader from "@/components/widgets/CircularLoader";
import useAuth from "@/app/hooks/useAuth";
import ProtectedRoute from "@/components/utils/ProtectedRoute";
import { motion } from "framer-motion";
import { SubjectGradesSkeleton } from "@/components/skeletons";
import { getTeacherPermissions, getTeacherNavigationItems } from "@/app/services/TeacherPermissionServices";
import { getGradeStats } from "@/app/services/GradeServices";
import { getCurrentTerm } from "@/app/services/TermServices";
import SubjectGradeCard from "@/components/grades/SubjectGradeCard";
import { useRouter } from "next/navigation";
import Link from "next/link";

interface SelectedSchool {
  school_id: string;
  school_name: string;
  access_granted_at: string;
}

interface SubjectWithStats {
  _id: string;
  name: string;
  teacherName: string;
  classAverage: number;
  gradeCount: number;
}

interface TeacherClassSubjectsPageProps {
  params: {
    classId: string;
  };
}

export default function TeacherClassSubjectsPage({ params }: TeacherClassSubjectsPageProps) {
  
  const classId = params.classId;
  const { user, logout } = useAuth();
  const router = useRouter();
  const [selectedSchool, setSelectedSchool] = useState<SelectedSchool | null>(null);
  // const [navigation, setNavigation] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [classData, setClassData] = useState<any>(null);
  const [subjects, setSubjects] = useState<SubjectWithStats[]>([]);
  const [currentTerm, setCurrentTerm] = useState<any>(null);
  const navigation = {
    icon: Percent,
    baseHref: `/teacher-dashboard/grades/class${classId}`,
    title: "Grades"
  };
  useEffect(() => {
    if (user) {
      // Get selected school from localStorage
      const storedSchool = localStorage.getItem("teacher_selected_school");
      if (storedSchool) {
        const school = JSON.parse(storedSchool);
        setSelectedSchool(school);
      } else {
        router.push("/teacher-dashboard");
      }
    } else {
      router.push("/teacher-dashboard");
    }
  }, [user, router]);

  useEffect(() => {
    const fetchData = async () => {
      if (!selectedSchool || !classId) return;

      try {
        setLoading(true);

        // Get navigation items
        // const navItems = await getTeacherNavigationItems(selectedSchool.school_id);
        // setNavigation(navItems);

        // Get teacher's assignments
        const teacherData = await getTeacherPermissions(selectedSchool.school_id);
        
        // Get current term
        const termData = await getCurrentTerm(selectedSchool.school_id);
        setCurrentTerm(termData);

        // Find the specific class
        const currentClass = teacherData.assigned_classes.find((cls: any) => cls._id === classId);
        if (!currentClass) {
          console.error("Class not found in teacher's assignments");
          router.push("/teacher-dashboard/grades");
          return;
        }
        setClassData(currentClass);

        // Get subjects for this class
        const classSubjects = teacherData.assigned_subjects.filter((subject: any) => subject.class_id === classId);
        
        // Get stats for each subject
        const subjectsWithStats: SubjectWithStats[] = [];
        
        for (const subject of classSubjects) {
          try {
            // Get grade stats for this subject in this class
            const filters = {
              class_id: classId,
              subject_id: subject._id || subject.name,
              ...(currentTerm && { term_id: currentTerm._id })
            };
            
            const statsResponse : any = await getGradeStats(selectedSchool.school_id, filters);
            console.log("Stats response:", statsResponse);
            const stats = statsResponse.stats || {};

            subjectsWithStats.push({
              _id: subject._id || subject.name,
              name: subject.name,
              teacherName: user?.first_name && user?.last_name
                ? `${user.first_name} ${user.last_name}`
                : user?.name || "Teacher",
              classAverage: stats.averageScore || 0,
              gradeCount: stats.totalGrades || 0
            });
          } catch (error) {
            console.warn(`Could not fetch stats for subject ${subject.name}:`, error);
            // Add subject with default stats
            subjectsWithStats.push({
              _id: subject._id || subject.name,
              name: subject.name,
              teacherName: user?.first_name && user?.last_name 
                ? `${user.first_name} ${user.last_name}` 
                : "Teacher",
              classAverage: 0,
              gradeCount: 0
            });
          }
        }

        setSubjects(subjectsWithStats);
        
      } catch (error) {
        console.error("Error fetching class subjects:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [selectedSchool, classId, user, router]);

  const handleSchoolChange = () => {
    localStorage.removeItem("teacher_selected_school");
    router.push("/teacher-dashboard");
  };

  const handleSubjectClick = (subjectId: string) => {
    router.push(`/teacher-dashboard/grades/class/subject?classId=${classId}__${classData._id}&subjectId=${subjectId}`);
  };

  const handleBackClick = () => {
    router.back();
  };

  if (loading) {
    return (
        <TeacherLayout
          navigation={navigation}
          selectedSchool={selectedSchool ? {
            _id: selectedSchool.school_id,
            name: selectedSchool.school_name
          } : null}
          onSchoolChange={handleSchoolChange}
          onLogout={logout}
        >
          <SubjectGradesSkeleton />
        </TeacherLayout>
    );
  }

  return (
      <TeacherLayout
        navigation={navigation}
        selectedSchool={selectedSchool ? {
          _id: selectedSchool.school_id,
          name: selectedSchool.school_name
        } : null}
        onSchoolChange={handleSchoolChange}
        onLogout={logout}
      >
        <div className="space-y-6">
          {/* Breadcrumb */}
          <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-300">
            <Link 
              href="/teacher-dashboard/grades"
              className="hover:text-teal transition-colors"
            >
              Grades
            </Link>
            <span>/</span>
            <span className="text-foreground font-medium">
              {classData?.name}
            </span>
          </div>

          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={handleBackClick}
                className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
              >
                <ArrowLeft className="h-5 w-5 text-foreground" />
              </button>
              <div>
                <h1 className="text-2xl font-bold text-foreground">
                  {classData?.name} - Subjects
                </h1>
                <p className="text-foreground/60">
                  Select a subject to manage grades
                  {currentTerm && (
                    <span className="ml-2 px-2 py-1 bg-teal/10 text-teal rounded-full text-xs">
                      {currentTerm.name} - {currentTerm.academic_year}
                    </span>
                  )}
                </p>
              </div>
            </div>
          </div>

          {/* Subjects Grid */}
          {subjects.length === 0 ? (
            <div className="text-center py-12">
              <BookOpen className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-foreground mb-2">
                No subjects available
              </h3>
              <p className="text-gray-500 dark:text-gray-400">
                You don't teach any subjects in this class yet
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {subjects.map((subject, index) => (
                <motion.div
                  key={subject._id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <SubjectGradeCard
                    subjectId={subject._id}
                    subjectName={subject.name}
                    teacherName={subject.teacherName}
                    classAverage={subject.classAverage}
                    gradeCount={subject.gradeCount}
                    onClick={() => handleSubjectClick(subject._id)}
                  />
                </motion.div>
              ))}
            </div>
          )}
        </div>
      </TeacherLayout>
  );
}
