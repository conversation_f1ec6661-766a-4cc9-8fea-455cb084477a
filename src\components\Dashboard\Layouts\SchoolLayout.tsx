"use client";
import React, { useState, useMemo } from "react";
import {
  FileCheck2, School, Users, LayoutDashboard, ChartNoAxesGantt, Clock4,
  ArrowLeftRight, Percent, Coins, Presentation, NotebookPen, GraduationCap,
  Settings, BookOpen, DollarSign, UserPlus, Megaphone, UserCog, Menu, X,
  CreditCard, Milestone, UserCheck, Calendar, FileText, AlertTriangle, BarChart3
} from "lucide-react";
import Divider from "../../widgets/Divider";
import SearchBox from "../../widgets/SearchBox";
import SidebarButton from "../SideNavButton";
import SidebarGroup from "../SidebarGroup";
import Avatar from "../Avatar";
import Logo from "../../widgets/Logo";
import GoPro from "../GoPro";
import NavigationBar from "../NavigationBar";
import useAuth from "@/app/hooks/useAuth";
import ProtectedRoute from "@/components/utils/ProtectedRoute";
import Breadcrumbs from "../BreadCrums";
import useSchoolAdminPermissions from "@/app/hooks/useSchoolAdminPermissions";

interface DashboardLayoutProps {
  navigation: {
    icon: React.ElementType;
    baseHref: string;
    title: string;
  };
  showGoPro?: boolean;
  onLogout: () => void;
  children: React.ReactNode;
}

const SchoolLayout: React.FC<DashboardLayoutProps> = ({
  navigation,
  showGoPro = true,
  onLogout,
  children,
}) => {
  const { user } = useAuth();
  // Get schoolId from user or context - adjust this based on your app structure
  const schoolId : any = user?.school_ids?.[0] || user?.schoolId;
  const { permissions, hasPermission, isLoading: loadingPermissions } = useSchoolAdminPermissions(schoolId);
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const avatar = {
    avatarUrl: user?.avatar || "https://img.freepik.com/free-psd/3d-illustration-person-with-sunglasses_23-2149436188.jpg",
    name: user?.name || "School Admin",
    role: user?.role || "admin",
  };
  const BASE_URL = "/school-admin";

  // Individual navigation items (not in groups)
  const individualNavItems = [
    { icon: LayoutDashboard, name: "Dashboard", href: `${BASE_URL}/dashboard` },
    { icon: School, name: "School", href: `${BASE_URL}/school`, permission: { module: 'school_management', action: 'view_school_info' } },
  ];

  // Helper function to check permissions
  const hasNavPermission = (module: string, permission: string): boolean => {
    if (!permissions) return false;
    return hasPermission(module as any, permission);
  };

  // Generate navigation groups based on permissions (memoized to prevent infinite re-renders)
  const navigationGroups = useMemo(() => {
    if (!permissions) {
      // Return empty groups if no permissions loaded
      return [];
    }

    const groups = [];

    // People Management group
    const peopleManagementItems = [];
    if (hasNavPermission('staff', 'view_staff_list')) {
      peopleManagementItems.push({ icon: UserCog, name: "Staff", href: `${BASE_URL}/staff` });
    }
    // if (hasNavPermission('staff', 'view_staff_list')) {
    //   peopleManagementItems.push({ icon: Users, name: "Teachers", href: `${BASE_URL}/teachers` });
    // }
    if (hasNavPermission('students', 'view_all_students')) {
      peopleManagementItems.push({ icon: GraduationCap, name: "Students", href: `${BASE_URL}/students` });
    }
    if (hasNavPermission('students', 'view_all_students')) {
      peopleManagementItems.push({ icon: UserPlus, name: "Parents", href: `${BASE_URL}/parents` });
    }

    if (peopleManagementItems.length > 0) {
      groups.push({
        title: "People Management",
        icon: Users,
        items: peopleManagementItems
      });
    }

    // Academic Records group
    const academicRecordsItems = [];
    if (hasNavPermission('academic_records', 'view_all_school_grades')) {
      academicRecordsItems.push({ icon: Percent, name: "Grades", href: `${BASE_URL}/grades` });
    }
    if (hasNavPermission('academic_records', 'manage_terms')) {
      academicRecordsItems.push({ icon: Calendar, name: "Terms", href: `${BASE_URL}/terms` });
    }
    if (hasNavPermission('academic_records', 'manage_timetables')) {
      academicRecordsItems.push({ icon: Clock4, name: "Time Table", href: `${BASE_URL}/timetable` });
    }
    if (hasNavPermission('staff', 'manage_teacher_assignments')) {
      academicRecordsItems.push({ icon: UserCheck, name: "Teacher Assignment", href: `${BASE_URL}/teacher-assignment` });
    }
    if (hasNavPermission('academic_records', 'manage_periods')) {
      academicRecordsItems.push({ icon: ChartNoAxesGantt, name: "Periods", href: `${BASE_URL}/period` });
    }
    if (hasNavPermission('academic_records', 'view_all_attendance')) {
      academicRecordsItems.push({ icon: FileCheck2, name: "Attendance", href: `${BASE_URL}/attendance` });
    }
    if (hasNavPermission('academic_records', 'manage_subjects')) {
      academicRecordsItems.push({ icon: NotebookPen, name: "Subjects", href: `${BASE_URL}/subjects` });
    }
    if (hasNavPermission('classes', 'view_all_classes')) {
      academicRecordsItems.push({ icon: Presentation, name: "Classes", href: `${BASE_URL}/classes` });
    }
    if (hasNavPermission('academic_records', 'manage_exam_types')) {
      academicRecordsItems.push({ icon: FileText, name: "Exam Types", href: `${BASE_URL}/examtype` });
    }
    if (hasNavPermission('academic_records', 'manage_discipline')) {
      academicRecordsItems.push({ icon: AlertTriangle, name: "Discipline", href: `${BASE_URL}/discipline` });
    }
    

    if (academicRecordsItems.length > 0) {
      groups.push({
        title: "Academic Records",
        icon: BookOpen,
        items: academicRecordsItems
      });
    }

    // Communications group
    const communicationsItems = [];
    if (hasNavPermission('communications', 'view_announcements')) {
      communicationsItems.push({ icon: Megaphone, name: "Announcements", href: `${BASE_URL}/announcements` });
    }
    if (hasNavPermission('communications', 'manage_resources')) {
      communicationsItems.push({ icon: BookOpen, name: "Resources", href: `${BASE_URL}/resources` });
    }

    if (communicationsItems.length > 0) {
      groups.push({
        title: "Communications",
        icon: Megaphone,
        items: communicationsItems
      });
    }

    // Financial group
    const financialItems = [];
    if (hasNavPermission('financials', 'manage_fee_types')) {
      financialItems.push({ icon: DollarSign, name: "Fee Types", href: `${BASE_URL}/fees` });
    }
    if (hasNavPermission('financials', 'view_transactions')) {
      financialItems.push({ icon: ArrowLeftRight, name: "Fee Transactions", href: `${BASE_URL}/transaction` });
    }
    if (hasNavPermission('financials', 'manage_school_credit_balance')) {
      financialItems.push({ icon: Coins, name: "Buy Credit", href: `${BASE_URL}/buy-credit` });
    }
    if (hasNavPermission('reports', 'generate_financial_reports') || hasNavPermission('reports', 'generate_student_reports')) {
      financialItems.push({ icon: BarChart3, name: "Reports & Analytics", href: `${BASE_URL}/reports` });
    }

    if (financialItems.length > 0) {
      groups.push({
        title: "Financial",
        icon: CreditCard,
        items: financialItems
      });
    }

    return groups;
  }, [permissions, hasNavPermission]);
    const settingsLink = {
    icon: Settings,
    name: "Settings",
    href: `${BASE_URL}/settings`
    };

  return (
    <ProtectedRoute allowedRoles={["admin","school_admin", "bursar", "dean_of_studies"]}>
      <div className="flex h-screen overflow-hidden sm:p-4">
        {/* Mobile Sidebar Toggle */}
        <button
          className="md:hidden p-2 bg-foreground text-background rounded-lg fixed top-4 left-4 z-50"
          onClick={() => setIsSidebarOpen(!isSidebarOpen)}
        >
          {isSidebarOpen ? <X size={24} /> : <Menu size={24} />}
        </button>

        {/* Sidebar */}
        <div
          className={`flex w-[290px] flex-col border border-gray-300 darK:border dark:border-gray-800 h-full shadow-lg p-2 rounded-lg fixed inset-y-0 left-0 z-40 bg-widget transition-transform lg:relative lg:translate-x-0 ${
            isSidebarOpen ? "translate-x-0" : "-translate-x-full"
          }`}
        >
          <div className="flex flex-col gap-3 overflow-auto subtle-scrollbar">
            <div className="flex flex-col items-center gap-2 my-4 ">
              <Logo />
              <Divider />
            </div>

            <SearchBox />

            <div className="flex flex-col gap-1">
              {/* Individual Navigation Items */}
              {individualNavItems.map((item) => (
                <SidebarButton
                  key={item.name}
                  icon={item.icon}
                  name={item.name}
                  href={item.href}
                />
              ))}

              {/* Divider */}
              <div className="my-2">
                <Divider />
              </div>

              {/* Grouped Navigation Items */}
              {loadingPermissions ? (
                <div className="flex items-center justify-center py-4">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-teal"></div>
                  <span className="ml-2 text-sm text-foreground/60">Loading permissions...</span>
                </div>
              ) : (
                navigationGroups.map((group) => (
                  <SidebarGroup
                    key={group.title}
                    title={group.title}
                    icon={group.icon}
                    items={group.items}
                    defaultExpanded={false} // Let the component handle expansion based on active routes
                  />
                ))
              )}
            </div>
          </div>

          {/* Footer */}
          <div className="mt-auto flex flex-col gap-3">
            {showGoPro && <GoPro visible />}
            <SidebarButton
              icon={settingsLink.icon}
              name={settingsLink.name}
              href={settingsLink.href}
            />
            <Divider />
            <Avatar
              avatarUrl={avatar.avatarUrl}
              name={avatar.name}
              role={avatar.role}
              onLogout={onLogout}
            />
          </div>
        </div>

        {/* Main Content */}
      <div className="sm:px-6 px-2 py-2 w-full flex flex-col gap-4 lg:w-[95%] overflow-auto custom-scrollbar">
        <div className="sticky top-0 z-20 flex items-center justify-between   ">
          <NavigationBar
            icon={navigation.icon}
            baseHref={navigation.baseHref}
            title={navigation.title}
            isSidebarOpen={isSidebarOpen}
            toggleSidebar={() => setIsSidebarOpen(!isSidebarOpen)}
            onLogout={onLogout}
          />
        </div>
        <div className="flex lg:hidden flex-col gap-2">
          <Breadcrumbs baseHref={navigation.baseHref} icon={navigation.icon} />
          <p className="text-2xl font-semibold text-foreground">{navigation.title}</p>
        </div>
        <div className="">{children}</div>
      </div>
      </div>
    </ProtectedRoute>
  );
};

export default SchoolLayout;
