"use client";

import React, { Suspense, useEffect, useState } from "react";
import { Per<PERSON>, ArrowLeft, Plus, Filter, Download, TrendingUp, Users, BookOpen } from "lucide-react";
import DeleteGradeModal from "../../components/DeleteGradeModal"; // Ensure this path is correct
import SchoolLayout from "@/components/Dashboard/Layouts/SchoolLayout";
import CircularLoader from "@/components/widgets/CircularLoader";
import useAuth from "@/app/hooks/useAuth";
import ProtectedRoute from "@/components/utils/ProtectedRoute";
import { motion } from "framer-motion";
import { SchoolAdminGradesSkeleton } from "@/components/skeletons";
import {
  getGradeRecords,
  getGradeStats,
  createGrade,
  updateGrade,
  deleteGrade,
  deleteMultipleGrades,
  exportGradesPDF,
  exportGradesExcel,
  getAvailableTerms,
  GradeRecord,
  GradeStats,
  GradeTerm,
  GradeSequence
} from "@/app/services/GradeServices";
import { getStudentsByClassAndSchool } from "@/app/services/StudentServices";
import { getSubjectById } from "@/app/services/SubjectServices";
import { getExamTypes } from "@/app/services/ExamTypeServices";
import { getClassById } from "@/app/services/ClassServices";
import { verifyPassword } from "@/app/services/UserServices"; // Now directly used for password verification
import GradeModal from "@/components/modals/GradeModal";
// PasswordConfirmDeleteModal is likely redundant if DeleteGradeModal now handles password
// import PasswordConfirmDeleteModal from "@/components/modals/PasswordConfirmDeleteModal";
import { useToast, ToastContainer } from "@/components/ui/Toast";
import { useRouter, useParams, useSearchParams } from "next/navigation";
import Link from "next/link";

// Define a type for the grouped grades
interface StudentGrades {
  student_id: string;
  student_name: string;
  grades: GradeRecord[];
}

// Accordion component
interface AccordionProps {
  children: React.ReactNode;
}

const Accordion: React.FC<AccordionProps> = ({ children }) => {
  return <div className="divide-y divide-gray-200 dark:divide-gray-700">{children}</div>;
};

// AccordionItem component
interface AccordionItemProps {
  title: React.ReactNode;
  children: React.ReactNode;
  isOpen: boolean;
  onToggle: () => void;
}

const AccordionItem: React.FC<AccordionItemProps> = ({ title, children, isOpen, onToggle }) => {
  return (
    <div className="my-2">
      <button
        className="flex justify-between items-center w-full p-4 text-left font-medium text-foreground bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-t-lg transition-colors duration-200"
        onClick={onToggle}
      >
        {title}
        <motion.span
          initial={false}
          animate={{ rotate: isOpen ? 180 : 0 }}
          transition={{ duration: 0.2 }}
        >
          <Plus className="w-5 h-5 text-gray-500 dark:text-gray-400" />
        </motion.span>
      </button>
      <motion.div
        initial={false}
        animate={{ height: isOpen ? "auto" : 0 }}
        transition={{ duration: 0.3 }}
        style={{ overflow: "hidden" }}
      >
        {isOpen && (
          <div className="p-4 bg-widget border-t border-gray-200 dark:border-gray-700 rounded-b-lg">
            {children}
          </div>
        )}
      </motion.div>
    </div>
  );
};


export default function SubjectGradesPage() {
  const { logout, user } = useAuth();
  const { toasts, removeToast, showSuccess, showError } = useToast();
  const router = useRouter();
  const searchParams = useSearchParams();
  const classId_combine = searchParams.get("classId") as string;
  const subjectId = searchParams.get("subjectId") as string;

  const classId = classId_combine?.split("__")[0] as string;
  const class_id = classId_combine?.split("__")[1] as string;

  // State management
  const [gradeRecords, setGradeRecords] = useState<GradeRecord[]>([]);
  const [groupedGrades, setGroupedGrades] = useState<StudentGrades[]>([]);
  const [expandedStudent, setExpandedStudent] = useState<string | null>(null); // To manage accordion state

  const [stats, setStats] = useState<GradeStats>({
    totalGrades: 0,
    averageScore: 0,
    highestScore: 0,
    lowestScore: 0,
    passRate: 0
  });
  const [classData, setClassData] = useState<any>(null);
  const [subjectData, setSubjectData] = useState<any>(null);
  const [students, setStudents] = useState<any[]>([]);
  const [examTypes, setExamTypes] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [isGradeModalOpen, setIsGradeModalOpen] = useState(false);
  const [gradeToEdit, setGradeToEdit] = useState<GradeRecord | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedGrades, setSelectedGrades] = useState<GradeRecord[]>([]);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [gradeToDelete, setGradeToDelete] = useState<GradeRecord | null>(null);
  const [deleteType, setDeleteType] = useState<"single" | "multiple">("single");
  const [clearSelection, setClearSelection] = useState(false); // This state isn't strictly necessary with current multi-delete UI

  // Filters - Enhanced with terms and sequences
  const [selectedTerm, setSelectedTerm] = useState<string>('all');
  const [selectedSequence, setSelectedSequence] = useState<string>('all');
  const [selectedExamType, setSelectedExamType] = useState<string>('all');

  // Terms data
  const [availableTerms, setAvailableTerms] = useState<GradeTerm[]>([]);
  const [currentTerm, setCurrentTerm] = useState<GradeTerm | null>(null);
  const [loadingTerms, setLoadingTerms] = useState(false);

  const schoolId: any = user?.school_ids?.[0] || user?.school_id;

  const navigation = {
    icon: Percent,
    baseHref: `/school-admin/grades/class/${classId}/subject/${subjectId}`,
    title: `${classData?.name} - ${subjectData?.name} Grades`
  };

  console.log(gradeToDelete)
  // Load available terms
  useEffect(() => {
    const loadTerms = async () => {
      if (!schoolId) return;

      try {
        setLoadingTerms(true);
        const termsData = await getAvailableTerms(schoolId);
        console.log(termsData)
        setAvailableTerms(termsData.terms);
        setCurrentTerm(termsData.current_term);

        // Auto-select current term if available
        // if (termsData.current_term && selectedTerm === 'all') {
        //   setSelectedTerm(termsData.current_term._id);
        // }
      } catch (error) {
        console.error('Error loading terms:', error);
      } finally {
        setLoadingTerms(false);
      }
    };

    loadTerms();
  }, [schoolId]);

  //console.log("her is the available ",availableTerms)

  // Function to group grades by student
  const groupGradesByStudent = (grades: GradeRecord[]): StudentGrades[] => {
    const studentMap = new Map<string, StudentGrades>();

    grades.forEach(grade => {
      if (!studentMap.has(grade.student_id)) {
        studentMap.set(grade.student_id, {
          student_id: grade.student_id,
          student_name: grade.student_name,
          grades: []
        });
      }
      studentMap.get(grade.student_id)?.grades.push(grade);
    });

    return Array.from(studentMap.values());
  };

  // Fetch grade data from API
  useEffect(() => {
    const fetchGradeData = async () => {
      if (!schoolId || !classId || !subjectId) return;

      try {
        setLoading(true);

        // Build filters for this specific class and subject
        const filters: any = {
          class_id: class_id,
          subject_id: subjectId
        };

        // Enhanced term filtering
        if (selectedTerm !== 'all') {
          filters.term_id = selectedTerm;
        }

        // Sequence filtering
        if (selectedSequence !== 'all') {
          filters.sequence_number = parseInt(selectedSequence);
        }

        // Exam type filtering
        if (selectedExamType !== 'all') {
          filters.exam_type_id = selectedExamType;
        }

        // Fetch records and stats in parallel
        const [recordsResponse, statsResponse] = await Promise.all([
          getGradeRecords(schoolId as string, filters),
          getGradeStats(schoolId as string, filters)
        ]);

        setGradeRecords(recordsResponse.grade_records);
        setStats(statsResponse.stats);
        setGroupedGrades(groupGradesByStudent(recordsResponse.grade_records)); // Group after fetching
      } catch (error) {
        console.error("Error fetching grade data:", error);
        showError("Error", "Failed to load grade data");
      } finally {
        setLoading(false);
      }
    };

    fetchGradeData();
  }, [schoolId, classId, subjectId, selectedTerm, selectedSequence, selectedExamType]);

  // Fetch additional data for modals
  useEffect(() => {
    const fetchAdditionalData = async () => {
      if (!schoolId || !classId || !subjectId) return;

      try {
        const [classResponse, subjectResponse, studentsResponse, examTypesResponse] = await Promise.all([
          getClassById(classId),
          getSubjectById(subjectId),
          getStudentsByClassAndSchool(class_id, schoolId),
          getExamTypes()
        ]);

        setClassData(classResponse);
        setSubjectData(subjectResponse);
        setStudents(studentsResponse);
        setExamTypes(examTypesResponse);
      } catch (error) {
        console.error("Error fetching additional data:", error);
      }
    };

    fetchAdditionalData();
  }, [schoolId, classId, subjectId]);

  // CRUD Functions
  const handleCreateGrade = () => {
    setGradeToEdit(null);
    setIsGradeModalOpen(true);
  };

  const handleEditGrade = (grade: GradeRecord) => {
    setGradeToEdit(grade);
    setIsGradeModalOpen(true);
  };

  const handleDeleteGrade = (grade: GradeRecord) => {
    setGradeToDelete(grade);
    setDeleteType("single");
    setIsDeleteModalOpen(true);
  };

  const handleDeleteMultiple = () => {
    // This is for multiple deletion, but not currently used in this UI (no checkboxes)
    // If you add checkboxes, you'd enable this button based on selectedGrades.length > 0
    if (selectedGrades.length === 0) {
      showError("No Selection", "Please select grades to delete.");
      return;
    }
    setDeleteType("multiple");
    setIsDeleteModalOpen(true);
  };

  const handleSelectionChange = (selectedRows: GradeRecord[]) => {
    // This is for selection, not currently used in this UI (no checkboxes)
    setSelectedGrades(selectedRows);
  };

  const handleGradeSubmit = async (data: any) => {
    setIsSubmitting(true);
    try {
      if (gradeToEdit) {
        // Update existing grade - include school_id as required by backend
        await updateGrade(gradeToEdit._id, {
          ...data,
          school_id: schoolId
        });
      } else {
        // Create new grade - ensure class and subject are set
        await createGrade({
          ...data,
          school_id: schoolId,
          class_id: classId,
          subject_id: subjectId
        });
      }

      // Refresh grades list
      const filters: any = {
        class_id: class_id,
        subject_id: subjectId
      };

      // Enhanced term filtering
      if (selectedTerm !== 'all') {
        filters.term_id = selectedTerm;
      }

      // Sequence filtering
      if (selectedSequence !== 'all') {
        filters.sequence_number = parseInt(selectedSequence);
      }

      // Exam type filtering
      if (selectedExamType !== 'all') {
        filters.exam_type_id = selectedExamType;
      }

      const [recordsResponse, statsResponse] = await Promise.all([
        getGradeRecords(schoolId as string, filters),
        getGradeStats(schoolId as string, filters)
      ]);

      setGradeRecords(recordsResponse.grade_records);
      setStats(statsResponse.stats);
      setGroupedGrades(groupGradesByStudent(recordsResponse.grade_records)); // Re-group after submit
      showSuccess("Success", gradeToEdit ? "Grade updated successfully" : "Grade created successfully");
    } catch (error: any) {
      showError("Error", error.message || "Failed to save grade");
    } finally {
      setIsSubmitting(false);
      setIsGradeModalOpen(false); // Close the modal after submission
      setGradeToEdit(null); // Clear gradeToEdit
    }
  };

  // Modified handleConfirmDelete to accept password
  const handleConfirmDelete = async (password: string) => {
    setIsSubmitting(true);
    try {
      // Verify password first
      if (!user?.email) {
        showError("Authentication Error", "User email not found. Deletion failed.");
        setIsSubmitting(false);
        return;
      }
      const isPasswordValid = await verifyPassword(password, user.email);
      if (!isPasswordValid) {
        showError("Authentication Error", "Incorrect password. Deletion failed.");
        setIsSubmitting(false);
        return;
      }

      if (deleteType === "single" && gradeToDelete) {
        await deleteGrade(gradeToDelete._id);
        showSuccess("Success", "Grade deleted successfully");
      } else if (deleteType === "multiple" && selectedGrades.length > 0) {
        // This part would be active if you implement multi-select checkboxes
        const gradeIdsToDelete = selectedGrades.map(grade => grade._id);
        await deleteMultipleGrades(gradeIdsToDelete);
        showSuccess("Success", "Selected grades deleted successfully");
        setSelectedGrades([]); // Clear selection after deletion
        setClearSelection(prev => !prev); // Trigger re-render for selection clear
      }
      // Re-fetch grades after deletion
      const filters: any = {
        class_id: class_id,
        subject_id: subjectId
      };

      if (selectedTerm !== 'all') {
        filters.term_id = selectedTerm;
      }
      if (selectedSequence !== 'all') {
        filters.sequence_number = parseInt(selectedSequence);
      }
      if (selectedExamType !== 'all') {
        filters.exam_type_id = selectedExamType;
      }

      const [recordsResponse, statsResponse] = await Promise.all([
        getGradeRecords(schoolId as string, filters),
        getGradeStats(schoolId as string, filters)
      ]);

      setGradeRecords(recordsResponse.grade_records);
      setStats(statsResponse.stats);
      setGroupedGrades(groupGradesByStudent(recordsResponse.grade_records));
    } catch (error: any) {
      console.error("Error deleting grade(s):", error);
      showError("Error", error.message || "Failed to delete grade(s)");
    } finally {
      setIsSubmitting(false);
      setIsDeleteModalOpen(false); // Close the delete modal
      setGradeToDelete(null); // Clear grade to delete
    }
  };

  const handleBackClick = () => {
    router.push(`/school-admin/grades/class?classId=${classId}`);
  };

  // Export functions
  const handleExportPDF = async () => {
    try {
      const filters: any = {
        class_id: class_id,
        subject_id: subjectId
      };

      // Enhanced term filtering
      if (selectedTerm !== 'all') {
        filters.term_id = selectedTerm;
      }

      // Sequence filtering
      if (selectedSequence !== 'all') {
        filters.sequence_number = parseInt(selectedSequence);
      }

      // Exam type filtering
      if (selectedExamType !== 'all') {
        filters.exam_type_id = selectedExamType;
      }

      const blob = await exportGradesPDF(schoolId!, filters);

      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `grades_${classData?.name}_${subjectData?.name}_${new Date().toISOString().split('T')[0]}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      showSuccess("Success", "Grades exported to PDF successfully");
    } catch (error) {
      console.error('Error exporting PDF:', error);
      showError("Error", "Failed to export grades to PDF");
    }
  };

  const handleExportExcel = async () => {
    try {
      const filters: any = {
        class_id: class_id,
        subject_id: subjectId
      };

      // Enhanced term filtering
      if (selectedTerm !== 'all') {
        filters.term_id = selectedTerm;
      }

      // Sequence filtering
      if (selectedSequence !== 'all') {
        filters.sequence_number = parseInt(selectedSequence);
      }

      // Exam type filtering
      if (selectedExamType !== 'all') {
        filters.exam_type_id = selectedExamType;
      }

      const blob = await exportGradesExcel(schoolId!, filters);

      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `grades_${classData?.name}_${subjectData?.name}_${new Date().toISOString().split('T')[0]}.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      showSuccess("Success", "Grades exported to Excel successfully");
    } catch (error) {
      console.error('Error exporting Excel:', error);
      showError("Error", "Failed to export grades to Excel");
    }
  };

  const getGradeColor = (grade: string) => {
    const splitGrade = grade.split("/")[0];
    switch (splitGrade) {
      case 'A+':
      case 'A':
        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';
      case 'B+':
      case 'B':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300';
      case 'C+':
      case 'C':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300';
      case 'D+':
      case 'D':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300';
      case 'E':
        return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
    }
  };

  // Add this helper to check if a grade is selected
  const isGradeSelected = (grade: GradeRecord) =>
    selectedGrades.some((g) => g._id === grade._id);

  // Add this handler to toggle selection
  const handleGradeCheckbox = (grade: GradeRecord) => {
    setSelectedGrades((prev) =>
      isGradeSelected(grade)
        ? prev.filter((g) => g._id !== grade._id)
        : [...prev, grade]
    );
  };

  // Add this handler to select/deselect all grades
  const handleSelectAll = () => {
    if (selectedGrades.length === gradeRecords.length) {
      setSelectedGrades([]);
    } else {
      setSelectedGrades(gradeRecords);
    }
  };

  // Merge all students with their grades (so students with no grades are included)
  const allStudentsWithGrades: StudentGrades[] = students.map((student) => {
    const found = groupedGrades.find((g) => String(g.student_id) === String(student.student_id));
    return {
      student_id: student._id,
      student_name: student.name || `${student.first_name} ${student.last_name}`,
      grades: found ? found.grades : [],
    };
  });

  console.log("students", students.map(s => s._id));
  console.log("groupedGrades", groupedGrades.map(g => g.student_id));

  if (loading) {
    return (
      <ProtectedRoute allowedRoles={["school_admin"]}>
        <SchoolLayout
          navigation={navigation}
          showGoPro={true}
          onLogout={logout}
        >
          <SchoolAdminGradesSkeleton />
        </SchoolLayout>
      </ProtectedRoute>
    );
  }

  return (
      <SchoolLayout
        navigation={navigation}
        showGoPro={true}
        onLogout={logout}
      >
        <div className="space-y-6">
          {/* Breadcrumb */}
          <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-300">
            <Link
              href="/school-admin/grades"
              className="hover:text-teal transition-colors"
            >
              Grades
            </Link>
            <span>/</span>
            <Link
              href={`/school-admin/grades/class?classId=${classId}`}
              className="hover:text-teal transition-colors"
            >
              {classData?.name}
            </Link>
            <span>/</span>
            <span className="text-foreground font-medium">
              {subjectData?.name}
            </span>
          </div>

          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleBackClick}
                className="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
              >
                <ArrowLeft className="w-5 h-5 text-gray-600 dark:text-gray-300" />
              </motion.button>
              <div>
                <h1 className="text-2xl font-bold text-foreground">
                  {classData?.name} - {subjectData?.name} Grades
                </h1>
                <p className="text-foreground/60">
                  Manage grades for this subject and class.
                </p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              {/* Export Buttons */}
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleExportPDF}
                className="bg-red-500 text-white px-4 py-2 rounded-lg flex items-center gap-2 hover:bg-red-600 transition-colors"
                disabled={gradeRecords.length === 0}
              >
                <Download className="w-4 h-4" />
                PDF
              </motion.button>

              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleExportExcel}
                className="bg-green-500 text-white px-4 py-2 rounded-lg flex items-center gap-2 hover:bg-green-600 transition-colors"
                disabled={gradeRecords.length === 0}
              >
                <Download className="w-4 h-4" />
                Excel
              </motion.button>

              {/* Add Grade Button */}
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleCreateGrade}
                className="bg-teal text-white px-4 py-2 rounded-lg flex items-center gap-2 hover:bg-teal-dark transition-colors"
              >
                <Plus className="w-4 h-4" />
                Add Grade
              </motion.button>
            </div>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="bg-widget p-4 rounded-lg border border-stroke">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-blue-500/10 rounded-lg flex items-center justify-center">
                  <TrendingUp className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Total Grades</p>
                  <p className="text-xl font-bold text-foreground">{stats.totalGrades}</p>
                </div>
              </div>
            </div>
            <div className="bg-widget p-4 rounded-lg border border-stroke">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-green-500/10 rounded-lg flex items-center justify-center">
                  <TrendingUp className="w-5 h-5 text-green-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Average Score</p>
                  <p className="text-xl font-bold text-foreground">{stats.averageScore.toFixed(1)}/20</p>
                </div>
              </div>
            </div>
            <div className="bg-widget p-4 rounded-lg border border-stroke">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-orange-500/10 rounded-lg flex items-center justify-center">
                  <TrendingUp className="w-5 h-5 text-orange-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Highest Score</p>
                  <p className="text-xl font-bold text-foreground">{stats.highestScore}/20</p>
                </div>
              </div>
            </div>
            <div className="bg-widget p-4 rounded-lg border border-stroke">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-purple-500/10 rounded-lg flex items-center justify-center">
                  <Users className="w-5 h-5 text-purple-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Pass Rate</p>
                  <p className="text-xl font-bold text-foreground">{stats.passRate.toFixed(1)}%</p>
                </div>
              </div>
            </div>
          </div>

          {/* Enhanced Filters with Terms and Sequences */}
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Term Filter */}
            <div className="flex-1">
              <select
                value={selectedTerm}
                onChange={(e) => {
                  setSelectedTerm(e.target.value);
                  // Reset sequence when term changes
                  setSelectedSequence('all');
                }}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-widget text-foreground focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent"
                disabled={loadingTerms}
              >
                <option value="all">All Terms</option>
                {availableTerms.map((term) => (
                  <option key={term._id} value={term._id}>
                    {term.name} ({term.academic_year})
                    {term.is_current && " - Current"}
                  </option>
                ))}
              </select>
            </div>

            {/* Sequence Filter */}
            <div className="flex-1">
              <select
                value={selectedSequence}
                onChange={(e) => setSelectedSequence(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-widget text-foreground focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent"
                disabled={selectedTerm === 'all'}
              >
                <option value="all">All Sequences</option>
                {selectedTerm !== 'all' &&
                  availableTerms
                    .find(term => term._id === selectedTerm)
                    ?.sequences.map((sequence) => (
                      <option key={sequence.sequence_number} value={sequence.sequence_number}>
                        {sequence.sequence_name}
                      </option>
                    ))
                }
              </select>
            </div>

            {/* Exam Type Filter */}
            <div className="flex-1">
              <select
                value={selectedExamType}
                onChange={(e) => setSelectedExamType(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-widget text-foreground focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent"
              >
                <option value="all">All Exam Types</option>
                {examTypes.map((type) => (
                  <option key={type._id} value={type._id}>
                    {type.type}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Show Delete Selected Button if any grades are selected */}
          {selectedGrades.length > 0 && (
            <div className="flex justify-end mb-2">
              <button
                className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
                onClick={handleDeleteMultiple}
                disabled={isSubmitting}
              >
                Delete Selected ({selectedGrades.length})
              </button>
            </div>
          )}

          {/* Grades in Accordion Style */}
          <div className="bg-widget rounded-lg border border-stroke p-4">
            {allStudentsWithGrades.length === 0 ? (
              <p className="text-center text-gray-500 dark:text-gray-400 py-8">No students found for this class.</p>
            ) : (
              <Accordion>
                {allStudentsWithGrades.map((studentData) => (
                  <AccordionItem
                    key={studentData.student_id}
                    title={<h3 className="text-lg font-semibold">{studentData.student_name}</h3>}
                    isOpen={expandedStudent === studentData.student_id}
                    onToggle={() =>
                      setExpandedStudent(
                        expandedStudent === studentData.student_id
                          ? null
                          : studentData.student_id
                      )
                    }
                  >
                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead className="bg-gray-50 dark:bg-gray-800">
                          <tr>
                            <th>
                              <input
                                type="checkbox"
                                checked={
                                  selectedGrades.length > 0 &&
                                  studentData.grades.every(isGradeSelected)
                                }
                                onChange={() => {
                                  // Select/deselect all grades for this student
                                  const allSelected = studentData.grades.every(isGradeSelected);
                                  setSelectedGrades((prev) => {
                                    if (allSelected) {
                                      // Remove all this student's grades
                                      return prev.filter(
                                        (g) => !studentData.grades.some((sg) => sg._id === g._id)
                                      );
                                    } else {
                                      // Add all this student's grades not already selected
                                      const toAdd = studentData.grades.filter(
                                        (sg) => !prev.some((g) => g._id === sg._id)
                                      );
                                      return [...prev, ...toAdd];
                                    }
                                  });
                                }}
                              />
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Term</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Exam Type</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Score (/20)</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Grade</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Comments</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200 dark:bg-gray-900 dark:divide-gray-700">
                          {studentData.grades.length > 0 ? (
                            studentData.grades.map((grade) => (
                              <tr key={grade._id}>
                                <td>
                                  <input
                                    type="checkbox"
                                    checked={isGradeSelected(grade)}
                                    onChange={() => handleGradeCheckbox(grade)}
                                  />
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                  <div className="text-sm">
                                    <div className="font-medium text-foreground">{grade.term}</div>
                                    {grade.sequence_name && (
                                      <div className="text-gray-500 dark:text-gray-400 text-xs">
                                        {grade.sequence_name}
                                      </div>
                                    )}
                                  </div>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-foreground">
                                  {grade.exam_type || (
                                    <span className="text-gray-400 italic text-sm">No exam type</span>
                                  )}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-foreground">{`${grade.score}/20`}</td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getGradeColor(grade.grade)}`}>
                                    {grade.grade}
                                  </span>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-foreground">{grade.comments || "-"}</td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-foreground">
                                  {new Date(grade.date_entered).toLocaleDateString()}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                  <button
                                    onClick={() => handleEditGrade(grade)}
                                    className="text-teal-600 hover:text-teal-900 dark:text-teal-400 dark:hover:text-teal-300 mr-3"
                                  >
                                    Edit
                                  </button>
                                  <button
                                    onClick={() => handleDeleteGrade(grade)}
                                    className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                                  >
                                    Delete
                                  </button>
                                </td>
                              </tr>
                            ))
                          ) : (
                            <tr>
                              <td colSpan={8} className="text-center text-gray-400 py-4">
                                No grades for this student.
                              </td>
                            </tr>
                          )}
                        </tbody>
                      </table>
                    </div>
                  </AccordionItem>
                ))}
              </Accordion>
            )}
          </div>
        </div>

        {/* Grade Modal for Create/Edit */}
        <GradeModal
          isOpen={isGradeModalOpen}
          onClose={() => {
            setIsGradeModalOpen(false);
            setGradeToEdit(null);
          }}
          onSubmit={handleGradeSubmit}
          grade={gradeToEdit}
          students={students}
          subjects={[subjectData].filter(Boolean)}
          examTypes={examTypes}
          loading={isSubmitting}
        />

        {/* Delete Grade Modal */}
        <DeleteGradeModal
          isOpen={isDeleteModalOpen}
          onClose={() => {
            setIsDeleteModalOpen(false);
            setGradeToDelete(null);
          }}
          onConfirm={handleConfirmDelete}
          loading={isSubmitting}
          deleteType={deleteType} // Pass the deleteType
          itemToDelete={gradeToDelete?.score ? `${gradeToDelete.score}/20 (${gradeToDelete.sequence_name})` : undefined} // Pass specific item for single delete
          numberOfItems={selectedGrades.length > 0 ? selectedGrades.length : undefined} // Pass count for multiple delete
        />

        {/* Toast Container */}
        <ToastContainer toasts={toasts} onClose={removeToast} />
      </SchoolLayout>
  );
}