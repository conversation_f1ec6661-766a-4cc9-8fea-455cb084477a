//routes/userRoutes.js
const express = require('express');
const userController = require('../controllers/userController');
const { uploadCSV, uploadUserAvatarImage} = require('../utils/uploaders');
const { authenticate, authorize, checkSubscription } = require('../middleware/middleware');
// 
const router = express.Router();
router.post('/register-user', authenticate, authorize(['admin', 'super', 'school_admin']), userController.registerUser);
router.post('/register-parent', authenticate, authorize(['admin', 'super', 'school_admin']), userController.registerParent);
router.post('/reset-parent-password', authenticate, authorize(['admin', 'super', 'school_admin']), userController.resetParentPassword);

router.get('/get-parents', authenticate, authorize(['admin', 'super']), userController.getParents);
router.get('/get-parents-by-page', authenticate, authorize(['admin', 'super']), userController.getParentsByPage);


// GET /users to fetch all users
router.get('/get-users', authenticate, checkSubscription, authorize(['admin', 'super', 'parent', 'teacher', 'school_admin', 'dean_of_studies', 'bursar']), userController.getAllUsers);
router.get('/search-users', authenticate, checkSubscription, authorize(['admin', 'super', 'parent', 'teacher', 'school_admin', 'dean_of_studies', 'bursar']), userController.searchUsers);

// Route to get user by user_id
router.get('/get-user/:id', authenticate, checkSubscription, authorize(['admin', 'super', 'parent', 'teacher', 'school_admin', 'dean_of_studies', 'bursar']), userController.getUserById);
router.get('/get-user-by-id/:id', authenticate, checkSubscription, authorize(['admin', 'super', 'parent', 'teacher', 'school_admin', 'dean_of_studies', 'bursar']), userController.getUserBy_id);
router.get('/get-user-email/:email', authenticate, checkSubscription, authorize(['admin', 'super', 'parent', 'teacher', 'school_admin', 'dean_of_studies', 'bursar']), userController.getUserByEmail);

// POST /users to create a new user
// router.post('/create-user', userController.createUser);

// PUT /users/:id to update a specific user
router.put('/update-user/:id', authenticate, checkSubscription, authorize(['admin', 'super', 'parent', 'teacher', 'school_admin', 'dean_of_studies', 'bursar']), userController.updateUserById);

// DELETE /users/:id to delete a specific user
router.delete('/delete-user/:id', authenticate, authorize(['admin', 'super', 'school_admin']), userController.deleteUserById);

//DELETE multiple users
router.delete('/delete-users', authenticate, authorize(['admin', 'super', 'school_admin']), userController.deleteMultipleUsers);

router.get(
  "/total-users",
  authenticate,
  checkSubscription,
  authorize(["super"]),
  userController.getTotalUsers
);

// Route to get number of users created this month and percentage change
router.get(
  "/users-count-change",
  authenticate,
  checkSubscription,
  authorize(["super"]),
  userController.getUserCountWithChange
);

router.get('/monthly-user-starts',  authenticate,authorize(["super"]), userController.getMonthlyUserStarts);



//DELETE ALL users
router.delete('/delete-all-users', authenticate, authorize(['super']), userController.deleteAllUsers);

// CHECK AUTH - Simple route to verify authentication
router.get('/check-auth', authenticate, (req, res) => {
  res.json({
    authenticated: true,
    user: req.user,
    timestamp: new Date().toISOString()
  });
});

router.post(
  '/upload-user-avatar/:id',
  uploadUserAvatarImage.single('avatar'),
  authenticate,
  authorize(['admin', 'super', 'school_admin', 'dean_of_studies']),
  userController.uploadUserAvatar
);

module.exports = router;
