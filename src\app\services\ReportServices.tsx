import { BASE_API_URL } from "./AuthContext";
import { getTokenFromCookie } from "./UserServices";

// Fonction pour obtenir les headers d'authentification
const getAuthHeaders = () => {
  const token = getTokenFromCookie("idToken");
  return {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`,
  };
};

// Interface pour les options d'export
export interface ExportOptions {
  format: 'pdf' | 'excel';
  period: 'week' | 'month' | 'quarter' | 'year';
  start_date?: string;
  end_date?: string;
}

// Interface pour la réponse d'export
export interface ExportResponse {
  success: boolean;
  message?: string;
  downloadUrl?: string;
}

// Exporter le rapport global (super-admin)
export async function exportGlobalReport(options: ExportOptions): Promise<void> {
  try {
    const queryParams = new URLSearchParams({
      format: options.format,
      period: options.period,
      ...(options.start_date && { start_date: options.start_date }),
      ...(options.end_date && { end_date: options.end_date })
    });

    const response = await fetch(`${BASE_API_URL}/school-subscription/reports/export/global?${queryParams}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${getTokenFromCookie("idToken")}`,
      },
    });

    if (!response.ok) {
      throw new Error(`Erreur lors de l'export: ${response.status}`);
    }

    // Télécharger le fichier
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    
    // Déterminer le nom du fichier basé sur le format
    const extension = options.format === 'pdf' ? 'pdf' : 'xlsx';
    const filename = `rapport-global-${new Date().toISOString().split('T')[0]}.${extension}`;
    link.download = filename;
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

  } catch (error: any) {
    console.error('Error exporting global report:', error);
    throw new Error(error.message || 'Erreur lors de l\'export du rapport global');
  }
}

// Exporter le rapport d'une école spécifique (school-admin)
export async function exportSchoolReport(schoolId: string, options: ExportOptions): Promise<void> {
  try {
    const queryParams = new URLSearchParams({
      format: options.format,
      period: options.period,
      ...(options.start_date && { start_date: options.start_date }),
      ...(options.end_date && { end_date: options.end_date })
    });

    const response = await fetch(`${BASE_API_URL}/school-subscription/${schoolId}/reports/export?${queryParams}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${getTokenFromCookie("idToken")}`,
      },
    });

    if (!response.ok) {
      throw new Error(`Erreur lors de l'export: ${response.status}`);
    }

    // Télécharger le fichier
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    
    // Déterminer le nom du fichier basé sur le format
    const extension = options.format === 'pdf' ? 'pdf' : 'xlsx';
    const filename = `rapport-ecole-${schoolId}-${new Date().toISOString().split('T')[0]}.${extension}`;
    link.download = filename;
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

  } catch (error: any) {
    console.error('Error exporting school report:', error);
    throw new Error(error.message || 'Erreur lors de l\'export du rapport de l\'école');
  }
}

// Fonction utilitaire pour valider les options d'export
export function validateExportOptions(options: ExportOptions): string[] {
  const errors: string[] = [];

  if (!['pdf', 'excel'].includes(options.format)) {
    errors.push('Format non supporté. Utilisez "pdf" ou "excel".');
  }

  if (!['week', 'month', 'quarter', 'year'].includes(options.period)) {
    errors.push('Période non supportée. Utilisez "week", "month", "quarter" ou "year".');
  }

  if (options.start_date && options.end_date) {
    const startDate = new Date(options.start_date);
    const endDate = new Date(options.end_date);
    
    if (startDate > endDate) {
      errors.push('La date de début doit être antérieure à la date de fin.');
    }
  }

  return errors;
}

// Fonction utilitaire pour obtenir les options de période par défaut
export function getDefaultPeriodOptions(): Array<{value: string, label: string}> {
  return [
    { value: 'week', label: 'Cette semaine' },
    { value: 'month', label: 'Ce mois' },
    { value: 'quarter', label: 'Ce trimestre' },
    { value: 'year', label: 'Cette année' }
  ];
}

// Fonction utilitaire pour obtenir les options de format
export function getFormatOptions(): Array<{value: string, label: string, description: string}> {
  return [
    { 
      value: 'pdf', 
      label: 'PDF', 
      description: 'Rapport formaté avec graphiques et tableaux' 
    },
    { 
      value: 'excel', 
      label: 'Excel', 
      description: 'Données brutes pour analyse approfondie' 
    }
  ];
}

// Fonction pour générer un nom de fichier personnalisé
export function generateFileName(type: 'global' | 'school', format: 'pdf' | 'excel', schoolId?: string): string {
  const date = new Date().toISOString().split('T')[0];
  const extension = format === 'pdf' ? 'pdf' : 'xlsx';
  
  if (type === 'global') {
    return `rapport-global-${date}.${extension}`;
  } else {
    return `rapport-ecole-${schoolId || 'unknown'}-${date}.${extension}`;
  }
}

// Fonction pour estimer la taille du rapport (approximative)
export function estimateReportSize(type: 'global' | 'school', format: 'pdf' | 'excel'): string {
  const sizes = {
    global: {
      pdf: '2-5 MB',
      excel: '1-3 MB'
    },
    school: {
      pdf: '500 KB - 2 MB',
      excel: '200 KB - 1 MB'
    }
  };
  
  return sizes[type][format];
}
