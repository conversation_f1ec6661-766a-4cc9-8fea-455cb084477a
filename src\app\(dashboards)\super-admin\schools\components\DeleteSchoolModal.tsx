"use client";

import React, { useState } from "react";
import { X } from "lucide-react";
import SubmissionFeedback from "@/components/widgets/SubmissionFeedback";
import { motion } from "framer-motion";
import CircularLoader from "@/components/widgets/CircularLoader";
import ActionButton from "@/components/ActionButton";
import { useTranslation } from "@/hooks/useTranslation";

interface DeleteSchoolModalProps {
  schoolName: string;
  onClose: () => void;
  onDelete: (password: string) => void;
  submitStatus: "success" | "failure" | null;
  isSubmitting: boolean;
}

const DeleteSchoolModal: React.FC<DeleteSchoolModalProps> = ({
  schoolName,
  onClose,
  onDelete,
  submitStatus,
  isSubmitting,
}) => {
  const { t } = useTranslation();
  const [password, setPassword] = useState("");

  const handleDelete = (e: React.FormEvent) => {
    e.preventDefault();
    if (!password) {
      alert(t('messages.validation.password_required'));
      return;
    }
    onDelete(password);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full max-w-md mx-4 sm:mx-6 md:mx-0 p-6 relative">
        {/* En-tête du modal */}
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-foreground">{t('components.modals.delete')} {t('navigation.schools')}</h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <X size={20} />
          </button>
        </div>

        {submitStatus ? (
          <SubmissionFeedback status={submitStatus}
            message={
              submitStatus === "success"
                ? t('messages.success.deleted')
                : t('messages.error.generic')
            } />
        ) : (
          <>
            {/* Message de confirmation */}
            <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
              {t('components.modals.delete_confirmation_with_name', { name: schoolName })} {t('components.modals.this_action_cannot_be_undone')}
            </p>

            {/* Champ de mot de passe */}
            <form onSubmit={handleDelete}>
              <div className="mb-6">
                <input
                  type="password"
                  placeholder={t('forms.placeholders.password_confirm_delete')}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full px-3 py-2 border border-pink-300 dark:border-pink-500 rounded-md text-sm text-foreground dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-pink-500"
                  required
                />
              </div>

              {/* Boutons */}
              <div className="flex justify-end space-x-2">
                <ActionButton
                  action="cancel"
                  onClick={onClose}
                />

                <ActionButton
                  action="delete"
                  type="submit"
                  isLoading={isSubmitting}
                  disabled={isSubmitting}
                  label={isSubmitting ? t('common.deleting') : t('common.delete')}
                />
              </div>
            </form>
          </>)}
      </div>
    </div>
  );
};

export default DeleteSchoolModal;