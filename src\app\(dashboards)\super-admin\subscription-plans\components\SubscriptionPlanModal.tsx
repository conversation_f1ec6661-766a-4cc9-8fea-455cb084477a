"use client";

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  X,
  Save,
  Plus,
  Trash2,
  Star,
  Users,
  CreditCard,
  AlertTriangle,
  CheckCircle,
  Loader2
} from 'lucide-react';
import {
  SubscriptionPlan,
  CreateSubscriptionPlanRequest,
  createSubscriptionPlan,
  updateSubscriptionPlan,
  validateSubscriptionPlan,
  generatePlanName,
  convertFeaturesToStrings,
  convertFeaturesToObjects
} from '@/app/services/SubscriptionPlanServices';
import { useTranslation } from '@/hooks/useTranslation';

interface SubscriptionPlanModalProps {
  plan?: SubscriptionPlan | null;
  onClose: () => void;
  onSuccess: () => void;
}

// Interface pour le formulaire (features toujours en string[])
interface FormData {
  plan_name: string;
  display_name: string;
  description: string;
  price_per_credit: number;
  minimum_purchase: number;
  maximum_purchase?: number;
  chatbot_enabled: boolean;
  chatbot_credits_per_purchase: number;
  features: string[];
  limitations: string[];
  recommended_for: string;
  max_students?: number;
  is_active: boolean;
  is_popular: boolean;
  sort_order: number;
  contact_required: boolean;
}

export default function SubscriptionPlanModal({ plan, onClose, onSuccess }: SubscriptionPlanModalProps) {
  const { t, tDashboard } = useTranslation();
  const [step, setStep] = useState<'form' | 'processing' | 'success' | 'error'>('form');
  const [formData, setFormData] = useState<FormData>({
    plan_name: '',
    display_name: '',
    description: '',
    price_per_credit: 0,
    minimum_purchase: 1,
    maximum_purchase: undefined,
    chatbot_enabled: false,
    chatbot_credits_per_purchase: 0,
    features: [''],
    limitations: [''],
    recommended_for: '',
    max_students: undefined,
    is_active: true,
    is_popular: false,
    sort_order: 1,
    contact_required: false
  });
  const [errors, setErrors] = useState<string[]>([]);
  const [error, setError] = useState<string | null>(null);

  const isEditing = !!plan;

  useEffect(() => {
    if (plan) {
      setFormData({
        plan_name: plan.plan_name,
        display_name: plan.display_name,
        description: plan.description,
        price_per_credit: plan.price_per_credit,
        minimum_purchase: plan.minimum_purchase,
        maximum_purchase: plan.maximum_purchase,
        chatbot_enabled: plan.chatbot_enabled,
        chatbot_credits_per_purchase: plan.chatbot_credits_per_purchase,
        features: convertFeaturesToStrings(plan.features),
        limitations: plan.limitations.length > 0 ? plan.limitations : [''],
        recommended_for: plan.recommended_for,
        max_students: plan.max_students,
        is_active: plan.is_active,
        is_popular: plan.is_popular,
        sort_order: plan.sort_order,
        contact_required: plan.contact_required
      });
    }
  }, [plan]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validation
    const validationErrors = validateSubscriptionPlan(formData as any);
    if (validationErrors.length > 0) {
      setErrors(validationErrors);
      return;
    }

    setStep('processing');
    setErrors([]);
    setError(null);

    try {
      // Nettoyer les données
      const cleanedData = {
        ...formData,
        features: convertFeaturesToObjects(formData.features.filter(f => f.trim() !== '')),
        limitations: formData.limitations.filter(l => l.trim() !== ''),
        plan_name: formData.plan_name || generatePlanName(formData.display_name)
      };

      if (isEditing && plan?._id) {
        await updateSubscriptionPlan(plan._id, cleanedData);
      } else {
        await createSubscriptionPlan(cleanedData);
      }

      setStep('success');
      setTimeout(() => {
        onSuccess();
      }, 1500);
    } catch (err: any) {
      console.error('Plan operation error:', err);
      setError(err.message || t('messages.error.generic'));
      setStep('error');
    }
  };

  const handleClose = () => {
    if (step !== 'processing') {
      onClose();
    }
  };

  const addFeature = () => {
    setFormData(prev => ({
      ...prev,
      features: [...prev.features, '']
    }));
  };

  const removeFeature = (index: number) => {
    setFormData(prev => ({
      ...prev,
      features: prev.features.filter((_, i) => i !== index)
    }));
  };

  const updateFeature = (index: number, value: string) => {
    setFormData(prev => ({
      ...prev,
      features: prev.features.map((f, i) => i === index ? value : f)
    }));
  };

  const addLimitation = () => {
    setFormData(prev => ({
      ...prev,
      limitations: [...prev.limitations, '']
    }));
  };

  const removeLimitation = (index: number) => {
    setFormData(prev => ({
      ...prev,
      limitations: prev.limitations.filter((_, i) => i !== index)
    }));
  };

  const updateLimitation = (index: number, value: string) => {
    setFormData(prev => ({
      ...prev,
      limitations: prev.limitations.map((l, i) => i === index ? value : l)
    }));
  };

  const renderForm = () => (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Informations de base */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">{tDashboard('super-admin', 'subscription-plans', 'basic_info')}</h3>
        
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Nom d'affichage *
            </label>
            <input
              type="text"
              value={formData.display_name}
              onChange={(e) => setFormData({ ...formData, display_name: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
              placeholder="Plan Basic"
              required
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Nom technique
            </label>
            <input
              type="text"
              value={formData.plan_name}
              onChange={(e) => setFormData({ ...formData, plan_name: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
              placeholder="basic (auto-généré si vide)"
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Description *
          </label>
          <textarea
            value={formData.description}
            onChange={(e) => setFormData({ ...formData, description: e.target.value })}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
            placeholder="Description du plan..."
            required
          />
        </div>
      </div>

      {/* Tarification */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">Tarification</h3>
        
        <div className="grid grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Prix par crédit (XAF) *
            </label>
            <input
              type="number"
              value={formData.price_per_credit}
              onChange={(e) => setFormData({ ...formData, price_per_credit: parseInt(e.target.value) || 0 })}
              min="0"
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
              required
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Achat minimum *
            </label>
            <input
              type="number"
              value={formData.minimum_purchase}
              onChange={(e) => setFormData({ ...formData, minimum_purchase: parseInt(e.target.value) || 1 })}
              min="1"
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
              required
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Achat maximum
            </label>
            <input
              type="number"
              value={formData.maximum_purchase || ''}
              onChange={(e) => setFormData({ ...formData, maximum_purchase: e.target.value ? parseInt(e.target.value) : undefined })}
              min="1"
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
              placeholder="Illimité"
            />
          </div>
        </div>
      </div>

      {/* Chatbot */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">Chatbot</h3>
        
        <div className="flex items-center space-x-3">
          <input
            type="checkbox"
            id="chatbot_enabled"
            checked={formData.chatbot_enabled}
            onChange={(e) => setFormData({ ...formData, chatbot_enabled: e.target.checked })}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label htmlFor="chatbot_enabled" className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Activer le chatbot pour ce plan
          </label>
        </div>

        {formData.chatbot_enabled && (
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Crédits chatbot par achat
            </label>
            <input
              type="number"
              value={formData.chatbot_credits_per_purchase}
              onChange={(e) => setFormData({ ...formData, chatbot_credits_per_purchase: parseInt(e.target.value) || 0 })}
              min="0"
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
            />
          </div>
        )}
      </div>

      {/* Fonctionnalités */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">Fonctionnalités</h3>
          <button
            type="button"
            onClick={addFeature}
            className="flex items-center space-x-1 text-blue-600 hover:text-blue-700"
          >
            <Plus className="h-4 w-4" />
            <span>Ajouter</span>
          </button>
        </div>
        
        {formData.features.map((feature, index) => (
          <div key={index} className="flex items-center space-x-2">
            <input
              type="text"
              value={feature}
              onChange={(e) => updateFeature(index, e.target.value)}
              className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
              placeholder="Fonctionnalité..."
            />
            {formData.features.length > 1 && (
              <button
                type="button"
                onClick={() => removeFeature(index)}
                className="p-2 text-red-600 hover:bg-red-50 dark:hover:bg-red-900/30 rounded-lg"
              >
                <Trash2 className="h-4 w-4" />
              </button>
            )}
          </div>
        ))}
      </div>

      {/* Options */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">Options</h3>
        
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Recommandé pour *
            </label>
            <input
              type="text"
              value={formData.recommended_for}
              onChange={(e) => setFormData({ ...formData, recommended_for: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
              placeholder="Petites écoles (1-100 étudiants)"
              required
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Ordre d'affichage *
            </label>
            <input
              type="number"
              value={formData.sort_order}
              onChange={(e) => setFormData({ ...formData, sort_order: parseInt(e.target.value) || 1 })}
              min="1"
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
              required
            />
          </div>
        </div>

        <div className="flex items-center space-x-6">
          <div className="flex items-center space-x-3">
            <input
              type="checkbox"
              id="is_active"
              checked={formData.is_active}
              onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="is_active" className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Plan actif
            </label>
          </div>

          <div className="flex items-center space-x-3">
            <input
              type="checkbox"
              id="is_popular"
              checked={formData.is_popular}
              onChange={(e) => setFormData({ ...formData, is_popular: e.target.checked })}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="is_popular" className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Plan populaire
            </label>
          </div>

          <div className="flex items-center space-x-3">
            <input
              type="checkbox"
              id="contact_required"
              checked={formData.contact_required}
              onChange={(e) => setFormData({ ...formData, contact_required: e.target.checked })}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="contact_required" className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Contact requis
            </label>
          </div>
        </div>
      </div>

      {/* Erreurs */}
      {errors.length > 0 && (
        <div className="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div className="flex items-start">
            <AlertTriangle className="h-5 w-5 text-red-500 mr-2 mt-0.5" />
            <div>
              <h4 className="text-red-700 dark:text-red-400 font-medium">Erreurs de validation :</h4>
              <ul className="text-red-600 dark:text-red-400 text-sm mt-1 list-disc list-inside">
                {errors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      )}

      {/* Actions */}
      <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
        <button
          type="button"
          onClick={handleClose}
          className="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
        >
          Annuler
        </button>
        <button
          type="submit"
          className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
        >
          <Save className="h-4 w-4" />
          <span>{isEditing ? 'Mettre à jour' : 'Créer'}</span>
        </button>
      </div>
    </form>
  );

  const renderProcessing = () => (
    <div className="text-center py-8">
      <Loader2 className="h-12 w-12 text-blue-500 mx-auto mb-4 animate-spin" />
      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
        {isEditing ? 'Mise à jour du plan...' : 'Création du plan...'}
      </h3>
      <p className="text-gray-600 dark:text-gray-400">
        Veuillez patienter pendant le traitement.
      </p>
    </div>
  );

  const renderSuccess = () => (
    <div className="text-center py-8">
      <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
        {isEditing ? 'Plan mis à jour !' : 'Plan créé !'}
      </h3>
      <p className="text-gray-600 dark:text-gray-400">
        Le plan de souscription a été {isEditing ? 'mis à jour' : 'créé'} avec succès.
      </p>
    </div>
  );

  const renderError = () => (
    <div className="text-center py-8">
      <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
        Erreur
      </h3>
      <p className="text-gray-600 dark:text-gray-400 mb-4">
        {error}
      </p>
      <button
        onClick={() => setStep('form')}
        className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
      >
        Réessayer
      </button>
    </div>
  );

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
      >
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {isEditing ? 'Modifier le plan' : 'Nouveau plan de souscription'}
          </h2>
          {step !== 'processing' && (
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <X className="h-6 w-6" />
            </button>
          )}
        </div>

        <div className="p-6">
          {step === 'form' && renderForm()}
          {step === 'processing' && renderProcessing()}
          {step === 'success' && renderSuccess()}
          {step === 'error' && renderError()}
        </div>
      </motion.div>
    </div>
  );
}
