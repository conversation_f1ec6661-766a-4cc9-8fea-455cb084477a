import { AttendanceJustificationUpdateSchema, PopulatedAttendanceJustification, SemiPopulatedJustification } from '@/app/models/Justification';
import { UserSchema } from '@/app/models/UserModel';
import { deleteJustification, getJustificationsBySchool, getJustificationsByStudent, reviewJustification, updateJustification } from '@/app/services/JustificationServices';
import { NotificationType } from '@/components/NotificationCard';
import React, { useState, useEffect } from 'react';

// Import Lucide React icons
import { Eye, Edit, Trash2, CheckCircle, XCircle, Loader2, Info, FileText, Image, Video, File, Gavel } from 'lucide-react';

// Assume a simple NotificationCard component exists. If not, you'll need to create one.
// This is a placeholder for the NotificationCard component
interface NotificationCardProps {
    message: string;
    type: NotificationType;
    onClose: () => void;
}

const NotificationCard: React.FC<NotificationCardProps> = ({ message, type, onClose }) => {
    const bgColor = type === "success" ? "bg-green-500" : type === "error" ? "bg-red-500" : "bg-blue-500";
    return (
        <div className={`fixed bottom-4 right-4 ${bgColor} text-white p-4 rounded-lg shadow-lg flex items-center justify-between z-[10000]`}>
            <span>{message}</span>
            <button onClick={onClose} className="ml-4 font-bold">X</button>
        </div>
    );
};


// Skeleton Loader Component
const TableSkeleton: React.FC = () => (
    <div className="animate-pulse">
        <div className="bg-gray-200 dark:bg-gray-700 h-10 w-full mb-4 rounded"></div>
        <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
                <div key={i} className="grid grid-cols-7 gap-4 items-center">
                    <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded col-span-1"></div>
                    <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded col-span-1"></div>
                    <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded col-span-1"></div>
                    <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded col-span-1"></div>
                    <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded col-span-1"></div>
                    <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded col-span-1"></div>
                    <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded col-span-1"></div>
                </div>
            ))}
        </div>
    </div>
);


function JustificationComponent({ user }: { user: UserSchema }) {
    const schoolId = user.school_ids?.[0] ?? null;

    const [justifications, setJustifications] = useState<PopulatedAttendanceJustification[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [isReviewModalOpen, setIsReviewModalOpen] = useState(false);
    const [selectedJustification, setSelectedJustification] = useState<PopulatedAttendanceJustification | null>(null);
    const [reviewStatus, setReviewStatus] = useState<'Pending' | 'Accepted' | 'Rejected'>('Pending');
    const [reviewComment, setReviewComment] = useState('');
    const [isNotificationCard, setIsNotificationCard] = useState(false);
    const [notificationMessage, setNotificationMessage] = useState("");
    const [notificationType, setNotificationType] = useState<NotificationType>("success");
    const [isSubmitting, setIsSubmitting] = useState(false); // New state for submission loading


    const fetchJustifications = async () => {
        setLoading(true);
        setError(null);
        try {
            let fetchedData: PopulatedAttendanceJustification[] = [];
            // Conditional fetching based on user role
            if (user.role === 'school_admin' || user.role === 'admin') {
                if (schoolId) {
                    fetchedData = await getJustificationsBySchool(schoolId as string);
                } else {
                    // Handle case where admin might need to select a school or view all
                    setError("School ID not available for admin. Please select a school.");
                }
            } else {
                setError("Unauthorized access or missing user ID.");
            }
            setJustifications(fetchedData);
        } catch (err) {
            console.error("Failed to fetch justifications:", err);
            setError("Failed to load justifications. Please try again.");
            setNotificationMessage("Failed to load justifications. Please try again.");
            setIsNotificationCard(true);
            setNotificationType("error");
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchJustifications();
    }, [user, schoolId]);

    const handleOpenReviewModal = (justification: PopulatedAttendanceJustification) => {
        setSelectedJustification(justification);
        setReviewStatus(justification.status);
        setReviewComment(justification.review_comment || '');
        setIsReviewModalOpen(true);
    };

    const handleCloseReviewModal = () => {
        setIsReviewModalOpen(false);
        setSelectedJustification(null);
        setReviewComment('');
        setReviewStatus('Pending');
    };

    const handleReviewSubmit = async () => {
        if (!selectedJustification) return;
        setIsSubmitting(true); // Start submission loading
        try {
            const updatedJustification = await reviewJustification(
                selectedJustification._id,
                reviewStatus,
                reviewComment
            );
            if (updatedJustification) {
                setJustifications((prev) =>
                    prev.map((j) =>
                        j._id === updatedJustification._id
                            ? ({ ...j, ...updatedJustification } as unknown as PopulatedAttendanceJustification)
                            : j
                    )
                );
                handleCloseReviewModal();
                setNotificationMessage("Review submitted successfully!");
                setIsNotificationCard(true);
                setNotificationType("success");
                fetchJustifications(); // Re-fetch to ensure data consistency
            } else {
                setError("Failed to review justification.");
                setNotificationMessage("Failed to review justification.");
                setIsNotificationCard(true);
                setNotificationType("error");
            }
        } catch (error) {
            const errorMessage =
                error instanceof Error
                    ? error.message
                    : "An unknown error occurred while submitting the review.";
            setNotificationMessage(errorMessage);
            setIsNotificationCard(true);
            setNotificationType("error");
        } finally {
            setIsSubmitting(false); // End submission loading
        }
    };

    const handleDeleteJustification = async (id: string) => {
        if (!window.confirm("Are you sure you want to delete this justification? This action cannot be undone.")) {
            return;
        }
        setLoading(true); // Global loading for delete operation
        try {
            const success = await deleteJustification(id);
            if (success) {
                setJustifications((prev) => prev.filter((j) => j._id !== id));
                setNotificationMessage("Justification deleted successfully!");
                setIsNotificationCard(true);
                setNotificationType("success");
            } else {
                setError("Failed to delete justification.");
                setNotificationMessage("Failed to delete justification.");
                setIsNotificationCard(true);
                setNotificationType("error");
            }
        } catch (err) {
            console.error("Error deleting justification:", err);
            setError("Error deleting justification. Please try again.");
            setNotificationMessage("Error deleting justification. Please try again.");
            setIsNotificationCard(true);
            setNotificationType("error");
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="p-4 sm:p-6 lg:p-8 min-h-screen text-foreground">
            <h1 className="text-3xl font-extrabold mb-8 text-center text-primary">Attendance Justifications</h1>

            {error && (
                <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
                    <strong className="font-bold">Error!</strong>
                    <span className="block sm:inline"> {error}</span>
                </div>
            )}

            {loading ? (
                <TableSkeleton />
            ) : justifications.length === 0 ? (
                <p className="text-center text-gray-500 py-10">No justifications found .</p>
            ) : (
                <div className="overflow-x-auto bg-card shadow-lg rounded-xl border border-border">
                    <table className="min-w-full divide-y divide-border">
                        <thead className="bg-muted">
                            <tr>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Date</th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Student</th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Type</th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Status</th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Reviewer</th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Comment</th>
                                <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-muted-foreground uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody className="bg-card divide-y divide-border">
                            {justifications.map((justification) => (
                                <tr key={justification._id} className="hover:bg-accent-foreground/5 transition-colors">
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-foreground">
                                        {new Date(justification.attendance_id.date).toLocaleDateString()}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-foreground">
                                        <div className="font-medium">
                                            {justification.attendance_id?.student_id?.first_name || 'Unknown'}{' '}
                                            {justification.attendance_id?.student_id?.last_name || ''}
                                        </div>
                                        <div className="text-xs text-muted-foreground">
                                            ({justification.attendance_id?.student_id?.student_id || 'N/A'})
                                        </div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-foreground">
                                        {justification.justification_type}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${justification.status === 'Accepted' ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100' :
                                            justification.status === 'Rejected' ? 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100' :
                                                'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100'
                                            }`}>
                                            {justification.status}
                                        </span>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-foreground">
                                        {justification.reviewed_by?.name || 'N/A'}
                                    </td>
                                    <td className="px-6 py-4 text-sm text-foreground max-w-xs truncate" title={justification.review_comment || 'No comment'}>
                                        {justification.review_comment || 'No comment'}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
                                        {/* Actions for School Admins/Admins */}
                                        {(user.role === 'school_admin' || user.role === 'admin') && (
                                            <button
                                                onClick={() => handleOpenReviewModal(justification)}
                                                className="inline-flex items-center justify-center p-2 rounded-full text-blue-600 hover:bg-blue-100 transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 mr-2"
                                                title="Review Justification"
                                            >
                                                <Gavel className="h-5 w-5" />
                                            </button>
                                        )}
                                        {/* Delete action (conditional based on role and status) */}
                                        {(justification.status === 'Pending') && (user.role === 'school_admin' || user.role === 'admin') && (
                                            <button
                                                onClick={() => handleDeleteJustification(justification._id)}
                                                className="inline-flex items-center justify-center p-2 rounded-full text-red-600 hover:bg-red-100 transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
                                                title="Delete Justification"
                                            >
                                                <Trash2 className="h-5 w-5" />
                                            </button>
                                        )}
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            )}

            {/* Review Justification Modal */}
            {isReviewModalOpen && selectedJustification && (
                <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center p-4 z-[9999]">
                    <div className="bg-background p-8 rounded-lg shadow-2xl w-full max-w-lg transform transition-all scale-100 opacity-100 animate-fade-in-up">
                        <h2 className="text-2xl font-bold mb-6 text-primary flex items-center">
                            <Gavel className="mr-2" /> Review Justification
                        </h2>
                        <div className="space-y-4 mb-6">
                            <p className="text-lg text-foreground">
                                <strong className="font-semibold">Student:</strong> {selectedJustification.attendance_id?.student_id?.first_name}{' '}
                                {selectedJustification.attendance_id?.student_id?.last_name}
                            </p>
                            <p className="text-foreground">
                                <strong className="font-semibold">Justification Text:</strong> {selectedJustification.text || 'No text provided'}
                            </p>
                            {selectedJustification.file_url && (
                                <div className="mt-4">
                                    <strong className="block text-foreground mb-2">Submitted File:</strong>
                                    {(() => {
                                        const url = selectedJustification.file_url;
                                        const extension = url.split('.').pop()?.toLowerCase() || '';

                                        if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(extension)) {
                                            return (
                                                <a href={url} target="_blank" rel="noopener noreferrer" className="block w-full h-48 overflow-hidden rounded-md border border-border bg-background flex items-center justify-center">
                                                    <Image className="h-10 w-10 text-muted-foreground absolute" />
                                                    <img src={url} alt="Submitted justification file" className="object-contain max-h-full max-w-full" />
                                                </a>
                                            );
                                        } else if (extension === 'pdf') {
                                            return (
                                                <a href={url} target="_blank" rel="noopener noreferrer" className="block w-full h-48 rounded-md border border-border bg-background flex items-center justify-center text-blue-600 hover:underline">
                                                    <FileText className="h-10 w-10 mr-2" /> View PDF Document
                                                </a>
                                            );
                                        } else if (['mp4', 'webm', 'ogg'].includes(extension)) {
                                            return (
                                                <video controls className="w-full h-48 rounded-md border border-border bg-background">
                                                    <source src={url} type={`video/${extension}`} />
                                                    Your browser does not support the video tag.
                                                </video>
                                            );
                                        } else {
                                            return (
                                                <a href={url} target="_blank" rel="noopener noreferrer" className="block w-full p-4 rounded-md border border-border bg-background flex items-center justify-center text-blue-600 hover:underline">
                                                    <File className="h-6 w-6 mr-2" /> View Submitted File
                                                </a>
                                            );
                                        }
                                    })()}
                                </div>
                            )}
                        </div>

                        <div className="mb-4 ">
                            <label htmlFor="status" className="block text-sm font-medium text-foreground mb-1">Status</label>
                            <div className="relative">
                                <select
                                    id="status"
                                    className="bg-background block w-full px-4 py-2 border border-border rounded-md shadow-sm focus:ring-primary focus:border-primary sm:text-sm bg-input text-foreground appearance-none pr-8"
                                    value={reviewStatus}
                                    onChange={(e) => setReviewStatus(e.target.value as 'Pending' | 'Accepted' | 'Rejected')}
                                >
                                    <option value="Pending">Pending</option>
                                    <option value="Accepted">Accepted</option>
                                    <option value="Rejected">Rejected</option>
                                </select>
                                <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-muted-foreground">
                                    <svg className="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                        <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                                    </svg>
                                </div>
                            </div>
                        </div>

                        <div className="mb-6">
                            <label htmlFor="comment" className="block text-sm font-medium text-foreground mb-1">Review Comment</label>
                            <textarea
                                id="comment"
                                rows={4}
                                className="block w-full p-3 border border-border rounded-md shadow-sm focus:ring-primary focus:border-primary sm:text-sm bg-input text-foreground bg-background resize-y"
                                value={reviewComment}
                                onChange={(e) => setReviewComment(e.target.value)}
                                placeholder="Add your review comments here..."
                            ></textarea>
                        </div>

                        <div className="flex justify-end space-x-3">
                            <button
                                onClick={handleCloseReviewModal}
                                className="inline-flex items-center px-4 py-2 border border-input rounded-md shadow-sm text-sm font-medium text-muted-foreground hover:bg-accent hover:text-accent-foreground focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors duration-200"
                                disabled={isSubmitting}
                            >
                                Cancel
                            </button>
                            <button
                                onClick={handleReviewSubmit}
                                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-teal hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors duration-200"
                                disabled={isSubmitting}
                            >
                                {isSubmitting ? (
                                    <>
                                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                        Submitting...
                                    </>
                                ) : (
                                    <>
                                        <CheckCircle className="mr-2 h-4 w-4" />
                                        Submit Review
                                    </>
                                )}
                            </button>
                        </div>
                    </div>
                </div>
            )}

            {isNotificationCard && (
                <NotificationCard
                    message={notificationMessage}
                    type={notificationType}
                    onClose={() => setIsNotificationCard(false)}
                />
            )}
        </div>
    );
}

export default JustificationComponent;