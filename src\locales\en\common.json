{"common": {"loading": "Loading...", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "add": "Add", "search": "Search", "filter": "Filter", "export": "Export", "import": "Import", "refresh": "Refresh", "back": "Back", "next": "Next", "previous": "Previous", "submit": "Submit", "confirm": "Confirm", "yes": "Yes", "no": "No", "close": "Close", "open": "Open", "view": "View", "download": "Download", "upload": "Upload", "select": "Select", "clear": "Clear", "reset": "Reset", "apply": "Apply", "actions": "Actions", "status": "Status", "active": "Active", "inactive": "Inactive", "enabled": "Enabled", "disabled": "Disabled", "success": "Success", "error": "Error", "warning": "Warning", "info": "Information", "required": "Required", "optional": "Optional", "name": "Name", "email": "Email", "phone": "Phone", "address": "Address", "date": "Date", "time": "Time", "description": "Description", "total": "Total", "amount": "Amount", "price": "Price", "quantity": "Quantity", "example_title": "Example: {title}", "unknown": "Unknown", "deleting": "Deleting...", "continue": "Continue", "notification": "Notification", "update": "Update", "create": "Create", "retry": "Retry", "sending": "Sending...", "view_all": "View all", "manage": "Manage", "not_available": "N/A", "gender": "Gender"}, "navigation": {"dashboard": "Dashboard", "schools": "Schools", "users": "Users", "students": "Students", "teachers": "Teachers", "parents": "Parents", "classes": "Classes", "subjects": "Subjects", "grades": "Grades", "attendance": "Attendance", "schedule": "Schedule", "reports": "Reports", "settings": "Settings", "profile": "Profile", "logout": "Logout", "notifications": "Notifications", "credits": "Credits", "subscriptions": "Subscriptions", "payments": "Payments", "analytics": "Analytics", "refunds": "Refunds"}, "dashboard": {"super-admin": {"pages": {"dashboard": {"title": "Super Admin Dashboard", "welcome": "Welcome, {name}", "overview": "Overview", "statistics": "Statistics", "recent_activities": "Recent Activities", "quick_actions": "Quick Actions", "total_schools": "Total Schools", "total_students": "Total Students", "total_teachers": "Total Teachers", "total_users": "Total Users", "active_subscriptions": "Active Subscriptions", "revenue": "Revenue (XAF)", "growth": "Growth"}, "schools": {"title": "School Management", "add_school": "Add School", "edit_school": "Edit School", "school_name": "School Name", "school_code": "School Code", "school_type": "School Type", "contact_person": "Contact Person", "subscription_plan": "Subscription Plan", "registration_date": "Registration Date", "last_activity": "Last Activity", "view_details": "View Details", "manage_credits": "Manage Credits", "view_analytics": "View Analytics", "principal_name": "Principal Name", "established_year": "Established Year", "website": "Website"}, "credits": {"title": "Credit Management", "available_credits": "Available Credits", "used_credits": "Used Credits", "total_credits": "Total Credits", "credit_history": "Credit History", "purchase_credits": "Purchase Credits", "credit_usage": "Credit Usage", "remaining_credits": "Remaining Credits", "credit_balance": "Credit Balance", "per_student": "per student", "per_message": "per message", "overview": "Overview of credits and analytics by school", "search_placeholder": "Search by school name or address...", "balance": "Balance", "revenue": "Revenue", "efficiency": "Efficiency", "remaining": "Remaining", "view_details": "View Details", "manage_title": "Credit Details", "manage_description": "Credit management and detailed analytics", "missing_school_id": "Missing school ID", "select_school_message": "Please select a school to view its details.", "back_to_list": "Back to list", "status": "Status", "current_balance": "Current Balance", "since_beginning": "Since beginning", "average_revenue_per_student": "Average revenue per student", "autonomy": "Autonomy", "estimated_days_remaining": "Estimated days remaining", "usage_metrics": "Usage Metrics", "registered_students": "Registered Students", "purchased_credits": "Purchased Credits", "purchase_count": "Purchase Count", "average_purchase": "Average Purchase", "recent_transactions": "Recent Transactions", "no_transactions_found": "No transactions found", "subscription_details": "Subscription Details", "plan_type": "Plan Type", "start_date": "Start Date", "activated": "activated", "new_transaction": "New Credit Transaction", "academic_year": "Academic Year", "select_academic_year": "Select academic year", "payment_method": "Payment Method", "amount_paid": "Amount <PERSON>", "send_credit": "Send Credit", "total_revenue": "Total Revenue"}, "subscriptions": {"title": "Subscription Management", "plan_basic": "Basic", "plan_standard": "Standard", "plan_custom": "Custom", "plan_features": "Plan Features", "subscription_status": "Subscription Status", "renewal_date": "Renewal Date", "upgrade_plan": "Upgrade Plan", "downgrade_plan": "Downgrade Plan", "cancel_subscription": "Cancel Subscription"}, "reports": {"title": "Reports", "generate_report": "Generate Report", "export_data": "Export Data", "date_range": "Date Range", "report_type": "Report Type"}, "users": {"title": "User Management", "add_user": "Add User", "edit_user": "Edit User", "user_role": "User Role", "user_status": "User Status", "last_login": "Last Login", "account_created": "Account Created", "all_roles": "All Roles", "filter_by_role": "Filter by Role", "user_details": "User Details", "delete_user": "Delete User", "bulk_delete": "Bulk Delete", "selected_users": "Selected Users"}, "parents": {"title": "Parent Management", "invite_parent": "<PERSON><PERSON><PERSON>", "parent_details": "Parent Details", "children": "Children", "contact_info": "Contact Information"}, "refunds": {"title": "Refund Management", "refund_request": "Refund Request", "refund_status": "Refund Status", "refund_amount": "Refund Amount", "process_refund": "Process Refund", "refund_reason": "Refund Reason"}, "subscription-plans": {"title": "Subscription Plans", "description": "Manage subscription plans and their features", "create_plan": "Create Plan", "edit_plan": "Edit Plan", "plan_details": "Plan Details", "plan_features": "Plan Features", "plan_pricing": "Plan Pricing", "active_plans": "Active Plans", "new_plan": "New Plan", "search_placeholder": "Search by name, description...", "all_plans": "All plans", "active": "Active", "inactive": "Inactive", "popular": "Popular", "total_plans": "Total plans", "popular_plans": "Popular plans", "with_chatbot": "With chatbot", "no_plans_found": "No plans found", "no_plans_match_criteria": "No plans match your search criteria.", "create_first_plan": "Start by creating your first subscription plan.", "min": "Min", "max": "Max", "chatbot": "<PERSON><PERSON><PERSON>", "features": "features", "order": "Order", "basic_info": "Basic Information", "display_name": "Display Name", "display_name_placeholder": "Basic Plan", "technical_name": "Technical Name", "technical_name_placeholder": "basic (auto-generated if empty)", "description_placeholder": "Plan description...", "pricing": "Pricing", "price_per_credit": "Price per credit (XAF)", "minimum_purchase": "Minimum purchase", "maximum_purchase": "Maximum purchase", "unlimited": "Unlimited", "enable_chatbot": "Enable chatbot for this plan", "chatbot_credits_per_purchase": "Chatbot credits per purchase", "feature_placeholder": "Feature...", "options": "Options", "recommended_for": "Recommended for", "recommended_for_placeholder": "Small schools (1-100 students)", "display_order": "Display order", "active_plan": "Active plan", "popular_plan": "Popular plan", "contact_required": "Contact required", "updating_plan": "Updating plan...", "creating_plan": "Creating plan...", "plan_updated": "Plan updated!", "plan_created": "Plan created!", "plan_updated_success": "The subscription plan has been successfully updated.", "plan_created_success": "The subscription plan has been successfully created."}, "classes": {"title": "Class Management", "search_placeholder": "Search schools by name or address...", "no_schools_found": "No schools found matching your search criteria.", "manage_classes": "Manage Classes", "manage_classes_of": "Manage Classes of", "class_level": "Class Level", "class_name": "Class Name", "class_code": "Class Code", "class_level_name": "Class Level Name", "add_new_class": "Add New Class", "add_new_level": "Add New Level", "all_class_levels": "All Class Levels", "edit_class": "Edit Class", "edit_level": "Edit Class Level", "select_level": "Select a level", "level_name_required": "Class Level name is required."}, "students": {"title": "Student Management", "school_id": "School ID", "school_name": "School Name", "student_id": "Student ID", "student_name": "Student Name", "birthday": "Birthday", "place_of_birth": "Place of Birth", "class_level": "Class Level", "parent_name": "Parent(s) Name", "registered": "Registered", "no_class": "No class", "no_guardian": "No guardian", "manage_students": "Manage Students", "manage_students_of": "Manage Students of", "popup_blocked_message": "Popup blocked! Please allow popups to view the student list.", "import_complete": "Import complete: {successful}/{total} students imported.", "register_student": "Register A Student", "upload_csv": "Upload CSV List", "print_student_list": "Print Student List", "print_id_cards": "Print ID Cards"}, "settings": {"title": "Settings", "general_settings": "General Settings", "system_settings": "System Settings", "notification_settings": "Notification Settings", "security_settings": "Security Settings", "backup_settings": "Backup Settings"}}}, "school-admin": {"pages": {"dashboard": {"title": "School Dashboard", "welcome": "Welcome, {name}", "overview": "School Overview", "statistics": "School Statistics", "recent_activities": "Recent Activities", "quick_actions": "Quick Actions", "total_students": "Total Students", "total_teachers": "Total Teachers", "total_classes": "Total Classes", "attendance_rate": "Attendance Rate", "academic_performance": "Academic Performance"}, "teachers": {"title": "Teacher Management", "add_teacher": "Add Teacher", "edit_teacher": "Edit Teacher", "teacher_id": "Teacher ID", "teacher_name": "Teacher Name", "subject_taught": "Subject Taught", "hire_date": "Hire Date", "qualification": "Qualification"}, "attendance": {"title": "Attendance Management", "mark_attendance": "Mark Attendance", "attendance_report": "Attendance Report", "present": "Present", "absent": "Absent", "late": "Late", "excused": "Excused"}, "grades": {"title": "Grade Management", "add_grade": "Add Grade", "edit_grade": "Edit Grade", "grade_book": "Grade Book", "subject": "Subject", "exam_type": "Exam Type", "score": "Score"}}}, "teacher-dashboard": {"pages": {"dashboard": {"title": "Teacher Dashboard", "welcome": "Welcome, {name}", "overview": "Overview", "my_classes": "My Classes", "today_schedule": "Today's Schedule", "recent_activities": "Recent Activities", "quick_actions": "Quick Actions", "students_count": "Students Count", "subjects_taught": "Subjects Taught", "upcoming_classes": "Upcoming Classes"}, "classes": {"title": "My Classes", "class_details": "Class Details", "student_list": "Student List", "class_schedule": "Class Schedule", "class_performance": "Class Performance"}, "attendance": {"title": "Attendance", "take_attendance": "Take Attendance", "attendance_history": "Attendance History", "mark_present": "<PERSON>", "mark_absent": "<PERSON>", "attendance_summary": "Attendance Summary"}, "grades": {"title": "Grades", "enter_grades": "Enter Grades", "grade_history": "Grade History", "grade_statistics": "Grade Statistics", "class_average": "Class Average", "student_progress": "Student Progress"}, "resources": {"title": "Resources", "upload_resource": "Upload Resource", "my_resources": "My Resources", "shared_resources": "Shared Resources", "resource_library": "Resource Library"}}}}, "components": {"chart": {"user_growth_trend": "User Growth Trend", "loading_user_stats": "Loading user stats...", "failed_to_load_data": "Failed to load data", "select_year": "Select Year"}, "performance_table": {"title": "School Performance", "school_name": "School Name", "metric": "Metric", "value": "Value", "search_placeholder": "Search for a school...", "items_per_page": "Items per page", "showing": "Showing", "to": "to", "of": "of", "entries": "entries", "previous": "Previous", "next": "Next", "no_data": "No data available", "select_metric": "Select Metric"}, "data_table": {"search": "Search...", "filter": "Filter", "export": "Export", "bulk_actions": "Bulk Actions", "select_all": "Select All", "deselect_all": "Deselect All", "delete_selected": "Delete Selected", "no_results": "No results found", "loading": "Loading...", "rows_per_page": "Rows per page", "page": "Page", "of_pages": "of {total} pages", "no_data": "No data available", "delete_selected_one": "Delete Selected (1)", "delete_selected_count": "Delete Selected ({count})", "view_details": "View Details", "selected_count": "{selected} of {total} selected", "select_all_count": "Select All ({count})", "delete_all_count": "Delete All ({count})", "active_filters": "Active filters", "page_of": "Page {current} of {total}", "items_per_page": "Items per page", "actions": "Actions", "all_items": "All {type}"}, "modals": {"create": "Create", "edit": "Edit", "delete": "Delete", "confirm": "Confirm", "cancel": "Cancel", "save": "Save", "close": "Close", "are_you_sure": "Are you sure?", "this_action_cannot_be_undone": "This action cannot be undone", "delete_confirmation": "Are you sure you want to delete this item?", "delete_confirmation_with_name": "Are you sure you want to delete <strong class=\"font-semibold text-pink-500\">{name}?</strong>", "delete_confirmation_prefix": "Are you sure you want to delete", "bulk_delete_confirmation": "Are you sure you want to delete {count} selected items?", "delete_all_items": "Delete All {type}", "delete_selected_items": "Delete Selected {type}", "delete_all_warning": "This will permanently delete ALL {type} in the system. This action cannot be undone!", "delete_selected_warning": "This will permanently delete the selected {type}. This action cannot be undone!", "danger_delete_all": "⚠️ DANGER: Delete All Operation", "bulk_delete_operation": "⚠️ Bulk Delete Operation", "about_to_delete_prefix": "You are about to delete", "delete_all_system_warning": "This will delete ALL {type} in the system and cannot be undone!", "enter_password_to_confirm": "Please enter your password to confirm this {type} operation:", "destructive": "destructive", "bulk": "bulk", "delete_count_items": "Delete {count} {type}"}}, "notifications": {"title": "Notifications", "mark_as_read": "<PERSON> <PERSON>", "mark_all_read": "<PERSON> as <PERSON>", "no_notifications": "No notifications", "new_notification": "New Notification", "notification_settings": "Notification Settings", "email_notifications": "Email Notifications", "push_notifications": "Push Notifications"}, "forms": {"validation": {"required_field": "This field is required", "invalid_email": "Invalid email address", "invalid_phone": "Invalid phone number", "password_too_short": "Password must be at least 8 characters", "passwords_dont_match": "Passwords do not match", "invalid_format": "Invalid format", "select_at_least_one": "Please select at least one item to delete", "no_items_to_delete": "No items to delete", "password_required": "Please enter your password to confirm deletion", "errors": "Validation errors", "fill_required_fields": "Please fill in all required fields.", "all_fields_required": "All fields are required."}, "placeholders": {"enter_name": "Enter name", "enter_email": "Enter email", "enter_phone": "Enter phone", "enter_address": "Enter address", "select_option": "Select an option", "search_placeholder": "Search...", "password": "Password", "confirm_password": "Confirm Password", "full_name": "Full Name", "role": "Role", "select_role": "Select Role", "select_school": "Select School", "website": "Website", "principal_name": "Principal Name", "established_year": "Established Year", "password_confirm_delete": "Type password to confirm delete"}}, "messages": {"success": {"saved": "Successfully saved", "deleted": "Successfully deleted", "updated": "Successfully updated", "created": "Successfully created", "sent": "Successfully sent", "all_items_deleted": "All {type} have been successfully deleted", "items_deleted": "{count} {type} successfully deleted", "bulk_delete": "Bulk delete was successful!", "transaction_saved": "Transaction saved successfully!", "class_created": "Class created successfully!", "level_created": "Level created successfully!", "class_deleted": "Class Deleted successfully!", "level_deleted": "Class level deleted successfully!", "student_deleted": "Student Deleted successfully!"}, "error": {"generic": "An error occurred", "network": "Network error", "unauthorized": "Unauthorized", "forbidden": "Access forbidden", "not_found": "Not found", "server_error": "Server error", "invalid_password": "Invalid Password!", "bulk_delete_failed": "Bulk delete failed. Please try again.", "loading_data": "Error loading data", "loading": "Loading error", "transaction_save_failed": "Error saving transaction. Please try again.", "class_creation_failed": "An unknown error occurred while creating the class.", "level_creation_failed": "An unknown error occurred while creating a class level.", "class_deletion_failed": "An unknown error occurred while deleting this class.", "level_deletion_failed": "An unknown error occurred while deleting this class level.", "student_deletion_failed": "An unknown error occurred while deleting the Student.", "import_failed": "Import failed."}, "confirmation": {"delete": "Are you sure you want to delete this item?", "cancel": "Are you sure you want to cancel?", "logout": "Are you sure you want to logout?"}, "info": {"please_wait": "Please wait while processing."}}, "registration": {"title": "Student Registration", "confirming": "Confirming...", "confirm_payment_register": "Confirm Payment & Register", "steps": {"personal_info": "Personal Information", "personal_info_desc": "Enter the student's details.", "contact_info": "Contact Information", "contact_info_desc": "Provide the student's contact info.", "guardian_details": "Parent/Guardian Details", "guardian_details_desc": "Provide information for parent/guardian.", "academic_info": "Academic Information", "academic_info_desc": "Details regarding the student's academic background.", "emergency_contact": "Emergency Contact", "emergency_contact_desc": "Provide emergency contact information.", "medical_info": "Medical Information", "medical_info_desc": "Fill in any medical history if applicable.", "consent": "Consent and Declaration", "consent_desc": "Agree to terms and conditions.", "fee_info": "Fee Information", "fee_info_desc": "Choose the fee structure and payment options.", "payment_confirmation": "Payment Confirmation", "payment_confirmation_desc": "Provide proof of payment and registration confirmation."}, "fields": {"student_address": "Student Address", "student_phone_optional": "Student Phone Number (Optional)", "select_class": "Select Class", "previous_school": "Previous School/Institution"}}, "language": {"french": "French", "english": "English", "select_language": "Select Language", "language_changed": "Language changed successfully"}}