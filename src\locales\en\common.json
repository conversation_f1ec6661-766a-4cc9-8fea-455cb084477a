{"common": {"loading": "Loading...", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "add": "Add", "search": "Search", "filter": "Filter", "export": "Export", "import": "Import", "refresh": "Refresh", "back": "Back", "next": "Next", "previous": "Previous", "submit": "Submit", "confirm": "Confirm", "yes": "Yes", "no": "No", "close": "Close", "open": "Open", "view": "View", "download": "Download", "upload": "Upload", "select": "Select", "clear": "Clear", "reset": "Reset", "apply": "Apply", "actions": "Actions", "status": "Status", "active": "Active", "inactive": "Inactive", "enabled": "Enabled", "disabled": "Disabled", "success": "Success", "error": "Error", "warning": "Warning", "info": "Information", "required": "Required", "optional": "Optional", "name": "Name", "email": "Email", "phone": "Phone", "address": "Address", "date": "Date", "time": "Time", "description": "Description", "total": "Total", "amount": "Amount", "price": "Price", "quantity": "Quantity", "example_title": "Example: {title}", "unknown": "Unknown", "deleting": "Deleting...", "continue": "Continue", "notification": "Notification"}, "navigation": {"dashboard": "Dashboard", "schools": "Schools", "users": "Users", "students": "Students", "teachers": "Teachers", "parents": "Parents", "classes": "Classes", "subjects": "Subjects", "grades": "Grades", "attendance": "Attendance", "schedule": "Schedule", "reports": "Reports", "settings": "Settings", "profile": "Profile", "logout": "Logout", "notifications": "Notifications", "credits": "Credits", "subscriptions": "Subscriptions", "payments": "Payments", "analytics": "Analytics"}, "dashboard": {"super-admin": {"pages": {"dashboard": {"title": "Super Admin Dashboard", "welcome": "Welcome, {name}", "overview": "Overview", "statistics": "Statistics", "recent_activities": "Recent Activities", "quick_actions": "Quick Actions", "total_schools": "Total Schools", "total_students": "Total Students", "total_teachers": "Total Teachers", "total_users": "Total Users", "active_subscriptions": "Active Subscriptions", "revenue": "Revenue (XAF)", "growth": "Growth"}, "schools": {"title": "School Management", "add_school": "Add School", "edit_school": "Edit School", "school_name": "School Name", "school_code": "School Code", "school_type": "School Type", "contact_person": "Contact Person", "subscription_plan": "Subscription Plan", "registration_date": "Registration Date", "last_activity": "Last Activity", "view_details": "View Details", "manage_credits": "Manage Credits", "view_analytics": "View Analytics", "principal_name": "Principal Name", "established_year": "Established Year", "website": "Website"}, "credits": {"title": "Credit Management", "available_credits": "Available Credits", "used_credits": "Used Credits", "total_credits": "Total Credits", "credit_history": "Credit History", "purchase_credits": "Purchase Credits", "credit_usage": "Credit Usage", "remaining_credits": "Remaining Credits", "credit_balance": "Credit Balance", "per_student": "per student", "per_message": "per message", "overview": "Overview of credit purchases and analytics by school", "search_placeholder": "Search for a school by name or address..."}, "subscriptions": {"title": "Subscription Management", "plan_basic": "Basic", "plan_standard": "Standard", "plan_custom": "Custom", "plan_features": "Plan Features", "subscription_status": "Subscription Status", "renewal_date": "Renewal Date", "upgrade_plan": "Upgrade Plan", "downgrade_plan": "Downgrade Plan", "cancel_subscription": "Cancel Subscription"}, "reports": {"title": "Reports", "generate_report": "Generate Report", "export_data": "Export Data", "date_range": "Date Range", "report_type": "Report Type"}, "users": {"title": "User Management", "add_user": "Add User", "edit_user": "Edit User", "user_role": "User Role", "user_status": "User Status", "last_login": "Last Login", "account_created": "Account Created", "all_roles": "All Roles", "filter_by_role": "Filter by Role", "user_details": "User Details", "delete_user": "Delete User", "bulk_delete": "Bulk Delete", "selected_users": "Selected Users"}, "students": {"title": "Student Management", "add_student": "Add Student", "edit_student": "Edit Student", "student_details": "Student Details", "enrollment_status": "Enrollment Status", "academic_year": "Academic Year", "class_level": "Class Level"}, "classes": {"title": "Class Management", "add_class": "Add Class", "edit_class": "Edit Class", "class_details": "Class Details", "class_capacity": "Class Capacity", "assigned_teacher": "Assigned Teacher"}, "parents": {"title": "Parent Management", "invite_parent": "<PERSON><PERSON><PERSON>", "parent_details": "Parent Details", "children": "Children", "contact_info": "Contact Information"}, "refunds": {"title": "Refund Management", "refund_request": "Refund Request", "refund_status": "Refund Status", "refund_amount": "Refund Amount", "process_refund": "Process Refund", "refund_reason": "Refund Reason"}, "subscription-plans": {"title": "Subscription Plans", "create_plan": "Create Plan", "edit_plan": "Edit Plan", "plan_details": "Plan Details", "plan_features": "Plan Features", "plan_pricing": "Plan Pricing", "active_plans": "Active Plans"}, "settings": {"title": "Settings", "general_settings": "General Settings", "system_settings": "System Settings", "notification_settings": "Notification Settings", "security_settings": "Security Settings", "backup_settings": "Backup Settings"}}}, "school-admin": {"pages": {"dashboard": {"title": "School Dashboard", "welcome": "Welcome, {name}", "overview": "School Overview", "statistics": "School Statistics", "recent_activities": "Recent Activities", "quick_actions": "Quick Actions", "total_students": "Total Students", "total_teachers": "Total Teachers", "total_classes": "Total Classes", "attendance_rate": "Attendance Rate", "academic_performance": "Academic Performance"}, "students": {"title": "Student Management", "add_student": "Add Student", "edit_student": "Edit Student", "student_id": "Student ID", "student_name": "Student Name", "class_level": "Class Level", "enrollment_date": "Enrollment Date", "parent_contact": "Parent Contact", "academic_year": "Academic Year"}, "teachers": {"title": "Teacher Management", "add_teacher": "Add Teacher", "edit_teacher": "Edit Teacher", "teacher_id": "Teacher ID", "teacher_name": "Teacher Name", "subject_taught": "Subject Taught", "hire_date": "Hire Date", "qualification": "Qualification"}, "classes": {"title": "Class Management", "add_class": "Add Class", "edit_class": "Edit Class", "class_name": "Class Name", "class_capacity": "Class Capacity", "assigned_teacher": "Assigned Teacher", "schedule": "Schedule"}, "attendance": {"title": "Attendance Management", "mark_attendance": "Mark Attendance", "attendance_report": "Attendance Report", "present": "Present", "absent": "Absent", "late": "Late", "excused": "Excused"}, "grades": {"title": "Grade Management", "add_grade": "Add Grade", "edit_grade": "Edit Grade", "grade_book": "Grade Book", "subject": "Subject", "exam_type": "Exam Type", "score": "Score"}}}, "teacher-dashboard": {"pages": {"dashboard": {"title": "Teacher Dashboard", "welcome": "Welcome, {name}", "overview": "Overview", "my_classes": "My Classes", "today_schedule": "Today's Schedule", "recent_activities": "Recent Activities", "quick_actions": "Quick Actions", "students_count": "Students Count", "subjects_taught": "Subjects Taught", "upcoming_classes": "Upcoming Classes"}, "classes": {"title": "My Classes", "class_details": "Class Details", "student_list": "Student List", "class_schedule": "Class Schedule", "class_performance": "Class Performance"}, "attendance": {"title": "Attendance", "take_attendance": "Take Attendance", "attendance_history": "Attendance History", "mark_present": "<PERSON>", "mark_absent": "<PERSON>", "attendance_summary": "Attendance Summary"}, "grades": {"title": "Grades", "enter_grades": "Enter Grades", "grade_history": "Grade History", "grade_statistics": "Grade Statistics", "class_average": "Class Average", "student_progress": "Student Progress"}, "resources": {"title": "Resources", "upload_resource": "Upload Resource", "my_resources": "My Resources", "shared_resources": "Shared Resources", "resource_library": "Resource Library"}}}}, "components": {"chart": {"user_growth_trend": "User Growth Trend", "loading_user_stats": "Loading user stats...", "failed_to_load_data": "Failed to load data", "select_year": "Select Year"}, "performance_table": {"title": "School Performance", "school_name": "School Name", "metric": "Metric", "value": "Value", "search_placeholder": "Search for a school...", "items_per_page": "Items per page", "showing": "Showing", "to": "to", "of": "of", "entries": "entries", "previous": "Previous", "next": "Next", "no_data": "No data available", "select_metric": "Select Metric"}, "data_table": {"search": "Search...", "filter": "Filter", "export": "Export", "bulk_actions": "Bulk Actions", "select_all": "Select All", "deselect_all": "Deselect All", "delete_selected": "Delete Selected", "no_results": "No results found", "loading": "Loading...", "rows_per_page": "Rows per page", "page": "Page", "of_pages": "of {total} pages", "no_data": "No data available", "delete_selected_one": "Delete Selected (1)", "delete_selected_count": "Delete Selected ({count})", "view_details": "View Details", "selected_count": "{selected} of {total} selected", "select_all_count": "Select All ({count})", "delete_all_count": "Delete All ({count})", "active_filters": "Active filters", "page_of": "Page {current} of {total}", "items_per_page": "Items per page", "actions": "Actions"}, "modals": {"create": "Create", "edit": "Edit", "delete": "Delete", "confirm": "Confirm", "cancel": "Cancel", "save": "Save", "close": "Close", "are_you_sure": "Are you sure?", "this_action_cannot_be_undone": "This action cannot be undone", "delete_confirmation": "Are you sure you want to delete this item?", "delete_confirmation_with_name": "Are you sure you want to delete <strong class=\"font-semibold text-pink-500\">{name}?</strong>", "delete_confirmation_prefix": "Are you sure you want to delete", "bulk_delete_confirmation": "Are you sure you want to delete {count} selected items?", "delete_all_items": "Delete All {type}", "delete_selected_items": "Delete Selected {type}", "delete_all_warning": "This will permanently delete ALL {type} in the system. This action cannot be undone!", "delete_selected_warning": "This will permanently delete the selected {type}. This action cannot be undone!", "danger_delete_all": "⚠️ DANGER: Delete All Operation", "bulk_delete_operation": "⚠️ Bulk Delete Operation", "about_to_delete_prefix": "You are about to delete", "delete_all_system_warning": "This will delete ALL {type} in the system and cannot be undone!", "enter_password_to_confirm": "Please enter your password to confirm this {type} operation:", "destructive": "destructive", "bulk": "bulk", "delete_count_items": "Delete {count} {type}"}}, "notifications": {"title": "Notifications", "mark_as_read": "<PERSON> <PERSON>", "mark_all_read": "<PERSON> as <PERSON>", "no_notifications": "No notifications", "new_notification": "New Notification", "notification_settings": "Notification Settings", "email_notifications": "Email Notifications", "push_notifications": "Push Notifications"}, "forms": {"validation": {"required_field": "This field is required", "invalid_email": "Invalid email address", "invalid_phone": "Invalid phone number", "password_too_short": "Password must be at least 8 characters", "passwords_dont_match": "Passwords do not match", "invalid_format": "Invalid format", "select_at_least_one": "Please select at least one item to delete", "no_items_to_delete": "No items to delete", "password_required": "Please enter your password to confirm deletion"}, "placeholders": {"enter_name": "Enter name", "enter_email": "Enter email", "enter_phone": "Enter phone", "enter_address": "Enter address", "select_option": "Select an option", "search_placeholder": "Search...", "password": "Password", "confirm_password": "Confirm Password", "full_name": "Full Name", "role": "Role", "select_role": "Select Role", "select_school": "Select School", "website": "Website", "principal_name": "Principal Name", "established_year": "Established Year", "password_confirm_delete": "Type password to confirm delete"}}, "messages": {"success": {"saved": "Successfully saved", "deleted": "Successfully deleted", "updated": "Successfully updated", "created": "Successfully created", "sent": "Successfully sent", "all_items_deleted": "All {type} have been successfully deleted", "items_deleted": "{count} {type} successfully deleted", "bulk_delete": "Bulk delete was successful!"}, "error": {"generic": "An error occurred", "network": "Network error", "unauthorized": "Unauthorized", "forbidden": "Access forbidden", "not_found": "Not found", "server_error": "Server error", "invalid_password": "Invalid Password!", "bulk_delete_failed": "Bulk delete failed. Please try again."}, "confirmation": {"delete": "Are you sure you want to delete this item?", "cancel": "Are you sure you want to cancel?", "logout": "Are you sure you want to logout?"}}, "language": {"french": "French", "english": "English", "select_language": "Select Language", "language_changed": "Language changed successfully"}}