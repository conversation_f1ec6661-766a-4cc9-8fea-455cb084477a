"use client";

import React, { useState, useEffect } from "react";
import { X, ChevronDown } from "lucide-react";
import SubmissionFeedback from "@/components/widgets/SubmissionFeedback";
import CircularLoader from "@/components/widgets/CircularLoader";
import { motion } from "framer-motion";
import { CreditTransactionSchema } from "@/app/models/CreditTransactionModel";
import { AcademicYearSchema } from "@/app/models/AcademicYear";
import { SchoolSchema } from "@/app/models/SchoolModel";
import ActionButton from "@/components/ActionButton";
import { useTranslation } from '@/hooks/useTranslation';

interface CreditTransactionModalProps {
  onClose: () => void;
  onSave: (data: CreditTransactionSchema) => Promise<void>;
  submitStatus: "success" | "failure" | null;
  isSubmitting: boolean;
  schools: SchoolSchema[];
  academicYears: AcademicYearSchema[];
}

const paymentMethods = ["manual", "gift"];
const COST_PER_CREDIT = 3000; // adjust as needed

const CreditTransactionModal: React.FC<CreditTransactionModalProps> = ({
  onClose,
  onSave,
  submitStatus,
  isSubmitting,
  schools,
  academicYears,
}) => {
  const { t, tDashboard } = useTranslation();
  const [formData, setFormData] = useState<CreditTransactionSchema>({
    _id: "",
    school_id: "",
    academicYear_id: "",
    payment_method: "manual", // Default to 'manual'
    amountPaid: 0,
    credit: 0,
    paidAt: "", // will be auto-generated on backend, not displayed
  });

  const [showPaymentMethodDropdown, setShowPaymentMethodDropdown] = useState(false);

  // Set default school and disable selection on initial load
  useEffect(() => {
    if (schools && schools.length > 0) {
      setFormData((prev) => ({
        ...prev,
        school_id: schools[0]._id || "", // Set the first school as default
      }));
    }
  }, [schools]); // Run when schools data changes

  // Update amountPaid whenever credit changes or payment method is gift
  useEffect(() => {
    setFormData((prev) => ({
      ...prev,
      amountPaid: prev.payment_method === "gift" ? 0 : prev.credit * COST_PER_CREDIT,
    }));
  }, [formData.credit, formData.payment_method]); // Added formData.payment_method as a dependency

  const togglePaymentMethodDropdown = () =>
    setShowPaymentMethodDropdown((v) => !v);

  const selectPaymentMethod = (method: "manual" | "gift") => {
    setFormData((prev) => ({ ...prev, payment_method: method }));
    setShowPaymentMethodDropdown(false);
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: name === "credit" ? Number(value) : value,
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (
      !formData.school_id ||
      !formData.academicYear_id ||
      !formData.payment_method ||
      // Condition for amountPaid depends on payment_method
      (formData.payment_method === "manual" && formData.amountPaid === 0) ||
      formData.credit === 0
    ) {
      alert(t('messages.validation.fill_required_fields'));
      return;
    }

    onSave(formData);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full max-w-lg p-6 relative">
        <button
          onClick={onClose}
          className="absolute top-3 right-3 text-gray-500 hover:text-gray-700"
          aria-label="Close modal"
        >
          <X size={20} />
        </button>

        <h2 className="text-xl font-semibold mb-4 text-foreground">
          {tDashboard('super-admin', 'credits', 'new_transaction')}
        </h2>

        {submitStatus ? (
          <SubmissionFeedback
            status={submitStatus}
            message={
              submitStatus === "success"
                ? "Transaction saved successfully!"
                : "Error saving transaction. Please try again."
            }
          />
        ) : (
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* School select - now disabled and defaults to the first school */}
            <div>
              <label htmlFor="school_id" className="block font-semibold mb-1">
                School <span className="text-red-600">*</span>
              </label>
              <select
                id="school_id"
                name="school_id"
                value={formData.school_id}
                onChange={handleChange}
                required
                disabled // Disable selection
                className="w-full px-3 py-2 border rounded-md dark:bg-gray-700 dark:text-white bg-gray-100 cursor-not-allowed"
              >
                {/* The default selected school will be displayed here */}
                {formData.school_id ? (
                  <option value={formData.school_id}>
                    {schools.find((s) => s._id === formData.school_id)?.name}
                  </option>
                ) : (
                  <option value="">Loading school...</option>
                )}
              </select>
            </div>

            {/* Academic Year select */}
            <div>
              <label htmlFor="academicYear_id" className="block font-semibold mb-1">
                Academic Year <span className="text-red-600">*</span>
              </label>
              <select
                id="academicYear_id"
                name="academicYear_id"
                value={formData.academicYear_id}
                onChange={handleChange}
                required
                className="w-full px-3 py-2 border rounded-md dark:bg-gray-700 dark:text-white"
              >
                <option value="">Select academic year</option>
                {academicYears.map((ay) => (
                  <option key={ay._id ?? ay.academic_year} value={ay._id ?? ay.academic_year}>
                    {ay.academic_year}
                  </option>
                ))}
              </select>
            </div>

            {/* Payment Method - now a selectable dropdown with "manual" and "gift" */}
            <div>
              <label htmlFor="payment_method" className="block font-semibold mb-1">
                Payment Method <span className="text-red-600">*</span>
              </label>
              <div className="relative">
                <button
                  type="button"
                  onClick={togglePaymentMethodDropdown}
                  className="w-full px-3 py-2 border rounded-md dark:bg-gray-700 dark:text-white text-left flex justify-between items-center"
                >
                  {formData.payment_method.charAt(0).toUpperCase() + formData.payment_method.slice(1)}
                  <ChevronDown size={16} />
                </button>
                {showPaymentMethodDropdown && (
                  <div className="absolute z-10 w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md mt-1 shadow-lg">
                    {paymentMethods.map((method) => (
                      <div
                        key={method}
                        onClick={() => selectPaymentMethod(method as "manual" | "gift")}
                        className="px-3 py-2 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600"
                      >
                        {method.charAt(0).toUpperCase() + method.slice(1)}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* Credit input */}
            <div>
              <label htmlFor="credit" className="block font-semibold mb-1">
                Credit <span className="text-red-600">*</span>
              </label>
              <input
                id="credit"
                name="credit"
                type="number"
                min={0}
                step={1}
                value={formData.credit}
                onChange={handleChange}
                required
                className="w-full px-3 py-2 border rounded-md dark:bg-gray-700 dark:text-white"
              />
            </div>

            {/* amountPaid - calculated and read-only */}
            <div>
              <label htmlFor="amountPaid" className="block font-semibold mb-1">
                Amount Paid (XAF)
              </label>
              <input
                id="amountPaid"
                name="amountPaid"
                type="text"
                value={new Intl.NumberFormat().format(formData.amountPaid)}
                readOnly
                className="w-full px-3 py-2 border rounded-md bg-gray-100 dark:bg-gray-700 dark:text-white cursor-not-allowed"
              />
            </div>

            <div className="flex justify-end space-x-2 mt-6">
              <ActionButton
                action="cancel"
                label="Cancel"
                onClick={onClose}
                disabled={isSubmitting}
              />

              <ActionButton
                action="sendCredit"
                label={isSubmitting ? 'Sending...' : 'Send Credit'}
                type="submit"
                isLoading={isSubmitting}
                disabled={isSubmitting}
              />
            </div>
          </form>
        )}
      </div>
    </div>
  );
};

export default CreditTransactionModal;