import { getTokenFrom<PERSON>ookie } from "./UserServices";

const BASE_API_URL = process.env.BASE_API_URL || "https://scolarify.onrender.com/api";

export interface TeacherPermissions {
  students: {
    view_all_students: boolean;
    add_edit_delete_students: boolean;
    generate_id_cards: boolean;
    generate_report_cards: boolean;
  };
  academic_records: {
    view_grades_assigned_classes: boolean;
    enter_edit_grades_assigned_classes: boolean;
    view_all_school_grades: boolean;
    take_attendance_assigned_classes: boolean;
    view_all_attendance: boolean;
  };
  financials: {
    view_student_fee_balances: boolean;
    record_fee_payments: boolean;
    manage_school_credit_balance: boolean;
    view_financial_reports: boolean;
  };
  staff: {
    view_staff_list: boolean;
    add_edit_delete_staff: boolean;
    manage_staff_permissions: boolean;
    reset_staff_passwords: boolean;
  };
  classes: {
    view_all_classes: boolean;
    add_edit_delete_classes: boolean;
    manage_class_schedules: boolean;
    assign_teachers_to_classes: boolean;
  };
  announcements: {
    view_announcements: boolean;
    create_edit_announcements: boolean;
    delete_announcements: boolean;
    publish_announcements: boolean;
  };
  resources: {
    view_resources: boolean;
    add_edit_delete_resources: boolean;
    manage_resource_categories: boolean;
  };
  reports: {
    generate_student_reports: boolean;
    generate_financial_reports: boolean;
    generate_attendance_reports: boolean;
    export_data: boolean;
  };
}

export interface TeacherAssignmentWithPermissions {
  _id: string;
  teacher_id: string;
  school_id: string;
  assigned_classes: Array<{
    class_code: string;
    class_code: string;
    _id: string;
    name: string;
    level: string;
  }>;
  assigned_subjects: Array<{
    _id: string;
    name: string;
    class_id: string;
    class_name: string;
  }>;
  permissions: TeacherPermissions;
  role_template: string;
}

// Get teacher's permissions and assignments for a specific school
export async function getTeacherPermissions(schoolId: string): Promise<TeacherAssignmentWithPermissions> {
  const token = getTokenFromCookie("idToken");

  if (!token) {
    throw new Error("Authentication token not found");
  }

  if (!schoolId) {
    throw new Error("School ID is required");
  }

  try {
    console.log('Fetching teacher permissions for school:', schoolId);

    const response = await fetch(`${BASE_API_URL}/teacher/assignments/${schoolId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ message: response.statusText }));
      console.error("Error fetching teacher permissions:", {
        status: response.status,
        statusText: response.statusText,
        error: errorData,
        schoolId
      });
      throw new Error(errorData.message || `Failed to fetch teacher permissions: ${response.status}`);
    }

    const data = await response.json();
    console.log('Teacher permissions response:', {
      assignedClasses: data.assigned_classes?.length || 0,
      assignedSubjects: data.assigned_subjects?.length || 0,
      roleTemplate: data.role_template
    });

    // Validate response structure
    if (!data.assigned_classes || !Array.isArray(data.assigned_classes)) {
      console.warn('Invalid teacher permissions response structure:', data);
      throw new Error("Invalid teacher permissions data received");
    }

    return data as TeacherAssignmentWithPermissions;
  } catch (error) {
    console.error("Fetch teacher permissions error:", error);
    if (error instanceof Error) {
      throw error;
    }
    throw new Error("Failed to fetch teacher permissions");
  }
}

// Check if teacher has a specific permission
export function hasPermission(
  permissions: TeacherPermissions,
  module: keyof TeacherPermissions,
  permission: string
): boolean {
  return permissions[module] && (permissions[module] as any)[permission] === true;
}

// Get navigation items based on teacher permissions
export function getTeacherNavigationItems(permissions: TeacherPermissions) {
  const navigationGroups: any[] = [];

  // My Classes group - always visible for teachers
  const myClassesGroup = {
    title: "My Classes",
    icon: "BookOpen",
    items: [] as Array<{ icon: string; name: string; href: string }>
  };

  if (hasPermission(permissions, 'classes', 'view_all_classes')) {
    myClassesGroup.items.push({ icon: "BookOpen", name: "Classes", href: "/teacher-dashboard/classes" });
  }

  if (hasPermission(permissions, 'students', 'view_all_students')) {
    myClassesGroup.items.push({ icon: "GraduationCap", name: "Students", href: "/teacher-dashboard/students" });
  }

  if (hasPermission(permissions, 'classes', 'manage_class_schedules')) {
    myClassesGroup.items.push({ icon: "Clock4", name: "Schedule", href: "/teacher-dashboard/timetable" });
  }

  // Add exam supervisions - always visible for teachers
  myClassesGroup.items.push({ icon: "Eye", name: "Exam Supervisions", href: "/teacher-dashboard/supervisions" });

  if (myClassesGroup.items.length > 0) {
    navigationGroups.push(myClassesGroup);
  }

  // Academic Tasks group
  const academicTasksGroup = {
    title: "Academic Tasks",
    icon: "ClipboardList",
    items: [] as Array<{ icon: string; name: string; href: string }>
  };

  if (hasPermission(permissions, 'academic_records', 'take_attendance_assigned_classes')) {
    academicTasksGroup.items.push({ icon: "FileCheck2", name: "Attendance", href: "/teacher-dashboard/attendance" });
  }

  if (hasPermission(permissions, 'academic_records', 'view_grades_assigned_classes')) {
    academicTasksGroup.items.push({ icon: "Percent", name: "Grades", href: "/teacher-dashboard/grades" });
  }

  if (academicTasksGroup.items.length > 0) {
    navigationGroups.push(academicTasksGroup);
  }

  // Resources group
  const resourcesGroup = {
    title: "Resources",
    icon: "Megaphone",
    items: [] as Array<{ icon: string; name: string; href: string }>
  };

  if (hasPermission(permissions, 'resources', 'view_resources')) {
    resourcesGroup.items.push({ icon: "BookOpen", name: "Teaching Materials", href: "/teacher-dashboard/resources" });
  }

  if (hasPermission(permissions, 'announcements', 'view_announcements')) {
    resourcesGroup.items.push({ icon: "Megaphone", name: "Announcements", href: "/teacher-dashboard/announcements" });
  }

  if (resourcesGroup.items.length > 0) {
    navigationGroups.push(resourcesGroup);
  }

  // Financial group (if teacher has financial permissions)
  const financialGroup = {
    title: "Financial",
    icon: "DollarSign",
    items: [] as Array<{ icon: string; name: string; href: string }>
  };

  if (hasPermission(permissions, 'financials', 'view_student_fee_balances')) {
    financialGroup.items.push({ icon: "CreditCard", name: "Fee Balances", href: "/teacher-dashboard/fees" });
  }

  if (hasPermission(permissions, 'financials', 'record_fee_payments')) {
    financialGroup.items.push({ icon: "Receipt", name: "Fee Payments", href: "/teacher-dashboard/fee-payments" });
  }

  if (financialGroup.items.length > 0) {
    navigationGroups.push(financialGroup);
  }

  // Reports group
  const reportsGroup = {
    title: "Reports",
    icon: "FileText",
    items: [] as Array<{ icon: string; name: string; href: string }>
  };

  if (hasPermission(permissions, 'reports', 'generate_student_reports')) {
    reportsGroup.items.push({ icon: "FileText", name: "Student Reports", href: "/teacher-dashboard/reports/students" });
  }

  if (hasPermission(permissions, 'reports', 'generate_attendance_reports')) {
    reportsGroup.items.push({ icon: "Calendar", name: "Attendance Reports", href: "/teacher-dashboard/reports/attendance" });
  }

  if (reportsGroup.items.length > 0) {
    navigationGroups.push(reportsGroup);
  }

  return navigationGroups;
}

// Get available actions for a specific module
export function getModuleActions(permissions: TeacherPermissions, module: keyof TeacherPermissions) {
  const modulePermissions = permissions[module];
  // @ts-ignore
  const actions = [];

  // Convert permissions to action capabilities
  Object.entries(modulePermissions).forEach(([permission, hasAccess]) => {
    if (hasAccess) {
      actions.push(permission);
    }
  });

  // @ts-ignore
  return actions;
}

// Get students in teacher's assigned classes
export async function getTeacherStudents(schoolId: string): Promise<any[]> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/teacher/students/${schoolId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      console.error("Error fetching teacher students:", response.statusText);
      throw new Error("Failed to fetch teacher students");
    }

    const data = await response.json();
    return data.students || [];
  } catch (error) {
    console.error("Fetch teacher students error:", error);
    throw new Error("Failed to fetch teacher students");
  }
}

// Get teacher's schedule based on assigned classes and periods
// Debug function to test teacher assignments (temporary)
export async function debugTeacherAssignments(schoolId: string): Promise<any> {
  const token = getTokenFromCookie("idToken");

  if (!token) {
    throw new Error("Authentication token not found");
  }

  if (!schoolId) {
    throw new Error("School ID is required");
  }

  try {
    console.log('🔍 Debugging teacher assignments for school:', schoolId);

    const response = await fetch(`${BASE_API_URL}/teacher/debug-assignments/${schoolId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ message: response.statusText }));
      console.error("Error debugging teacher assignments:", {
        status: response.status,
        statusText: response.statusText,
        error: errorData,
        schoolId
      });
      throw new Error(errorData.message || `Failed to debug teacher assignments: ${response.status}`);
    }

    const data = await response.json();
    console.log('🔍 Debug response:', data);

    return data;
  } catch (error) {
    console.error("Debug teacher assignments error:", error);
    if (error instanceof Error) {
      throw error;
    }
    throw new Error("Failed to debug teacher assignments");
  }
}

export async function getTeacherSchedule(schoolId: string): Promise<any[]> {
  const token = getTokenFromCookie("idToken");

  if (!token) {
    throw new Error("Authentication token not found");
  }

  if (!schoolId) {
    throw new Error("School ID is required");
  }

  try {
    console.log('Fetching teacher schedule for school:', schoolId);

    const response = await fetch(`${BASE_API_URL}/teacher/schedule/${schoolId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ message: response.statusText }));
      console.error("Error fetching teacher schedule:", {
        status: response.status,
        statusText: response.statusText,
        error: errorData,
        schoolId
      });
      throw new Error(errorData.message || `Failed to fetch teacher schedule: ${response.status}`);
    }

    const data = await response.json();
    console.log('Teacher schedule response:', {
      scheduleCount: data.schedule?.length || 0,
      sampleSchedule: data.schedule?.[0] || null
    });

    // Validate response structure
    if (!data.schedule || !Array.isArray(data.schedule)) {
      console.warn('Invalid teacher schedule response structure:', data);
      return [];
    }

    return data.schedule;
  } catch (error) {
    console.error("Fetch teacher schedule error:", error);
    if (error instanceof Error) {
      throw error;
    }
    throw new Error("Failed to fetch teacher schedule");
  }
}
