"use client";

import SuperLayout from "@/components/Dashboard/Layouts/SuperLayout";
import useAuth from "@/app/hooks/useAuth";
import CircularLoader from "@/components/widgets/CircularLoader";
import React, { Suspense, useEffect, useState } from "react";
import { 
  Coins, 
  ArrowLeft, 
  TrendingUp, 
  Users, 
  DollarSign, 
  Calendar, 
  CreditCard, 
  BarChart3, 
  Target, 
  Clock,
  Building2,
  MapPin,
  Mail,
  Phone,
  Download,
  RefreshCw,
  Plus,
  AlertTriangle,
  CheckCircle
} from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import { getSchoolBy_id } from "@/app/services/SchoolServices";
import { SchoolSchema } from "@/app/models/SchoolModel";
import { motion } from "framer-motion";
import {
  getSchoolCompleteAnalytics,
  getCreditPurchaseHistory,
  formatCurrency,
  formatCredits,
  getPlanTypeText,
  getPlanTypeColor,
  getPaymentStatusText,
  getPaymentStatusColor,
  SchoolCompleteAnalyticsResponse,
  CreditPurchaseSchema
} from "@/app/services/CreditPurchaseServices";
import { getRegisteredStudentsStats, RegisteredStudentsStatsSchema } from '@/app/services/StudentServices';
import { useAcademicYearContext } from '@/context/AcademicYearContext';
import { useTranslation } from '@/hooks/useTranslation';
import ExportReportModal from '@/components/modals/ExportReportModal';

export default function SchoolCreditManagePage() {
    const { t, tDashboard } = useTranslation();
    const { currentAcademicYear } = useAcademicYearContext();
    const BASE_URL = "/super-admin";
    const { user, logout } = useAuth();
    const router = useRouter();
    const searchParams = useSearchParams();
    const schoolId = searchParams.get('id');

    const [school, setSchool] = useState<SchoolSchema | null>(null);
    const [analytics, setAnalytics] = useState<SchoolCompleteAnalyticsResponse | null>(null);
    const [studentStats, setStudentStats] = useState<RegisteredStudentsStatsSchema | null>(null);
    const [purchases, setPurchases] = useState<CreditPurchaseSchema[]>([]);
    const [loading, setLoading] = useState(true);
    const [loadingPurchases, setLoadingPurchases] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [isExportModalOpen, setIsExportModalOpen] = useState(false);

    const navigation = {
        icon: Coins,
        baseHref: `${BASE_URL}/credit/manage`,
        title: tDashboard('super-admin', 'credits', 'manage_title'),
    };

    useEffect(() => {
        if (schoolId) {
            fetchSchoolData();
        }
    }, [schoolId]);

    const fetchSchoolData = async () => {
        try {
            setLoading(true);
            setError(null);

            if (!currentAcademicYear) {
                throw new Error('Academic year not available');
            }

            const [schoolData, analyticsData, studentStatsData] = await Promise.all([
                getSchoolBy_id(schoolId!),
                getSchoolCompleteAnalytics(schoolId!),
                getRegisteredStudentsStats(schoolId!, currentAcademicYear)
            ]);

            setSchool(schoolData);
            setAnalytics(analyticsData);
            setStudentStats(studentStatsData);

            // Fetch purchase history
            setLoadingPurchases(true);
            const purchaseHistory = await getCreditPurchaseHistory(schoolId!, 20);
            setPurchases(purchaseHistory);

        } catch (err: any) {
            setError(err.message || t('messages.error.loading_data'));
        } finally {
            setLoading(false);
            setLoadingPurchases(false);
        }
    };

    const handleRefresh = async () => {
        await fetchSchoolData();
    };

    const handleExport = () => {
        setIsExportModalOpen(true);
    };

    if (!schoolId) {
        return (
            <SuperLayout navigation={navigation} showGoPro={true} onLogout={() => logout()}>
                <div className="p-6">
                    <div className="text-center py-12">
                        <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
                        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                            {tDashboard('super-admin', 'credits', 'missing_school_id')}
                        </h3>
                        <p className="text-gray-600 dark:text-gray-400">
                            {tDashboard('super-admin', 'credits', 'select_school_message')}
                        </p>
                        <button
                            onClick={() => router.push(`${BASE_URL}/credit`)}
                            className="mt-4 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg"
                        >
                            {tDashboard('super-admin', 'credits', 'back_to_list')}
                        </button>
                    </div>
                </div>
            </SuperLayout>
        );
    }

    if (loading) {
        return (
            <SuperLayout navigation={navigation} showGoPro={true} onLogout={() => logout()}>
                <div className="flex items-center justify-center min-h-screen">
                    <CircularLoader />
                </div>
            </SuperLayout>
        );
    }

    if (error) {
        return (
            <SuperLayout navigation={navigation} showGoPro={true} onLogout={() => logout()}>
                <div className="p-6">
                    <div className="text-center py-12">
                        <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
                        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                            {t('messages.error.loading')}
                        </h3>
                        <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
                        <button
                            onClick={handleRefresh}
                            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg"
                        >
                            {t('common.retry')}
                        </button>
                    </div>
                </div>
            </SuperLayout>
        );
    }

    return (
        <SuperLayout navigation={navigation} showGoPro={true} onLogout={() => logout()}>
            <div className="p-6 space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                        <button
                            onClick={() => router.push(`${BASE_URL}/credit`)}
                            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                        >
                            <ArrowLeft className="h-5 w-5 text-gray-600 dark:text-gray-400" />
                        </button>
                        <div>
                            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                                {school?.name}
                            </h1>
                            <p className="text-gray-600 dark:text-gray-400 mt-1">
                                {tDashboard('super-admin', 'credits', 'manage_description')}
                            </p>
                        </div>
                    </div>
                    <div className="flex items-center space-x-3">
                        <button
                            onClick={handleRefresh}
                            className="flex items-center px-4 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors"
                        >
                            <RefreshCw className="h-4 w-4 mr-2" />
                            {t('common.refresh')}
                        </button>
                        <button
                            onClick={handleExport}
                            className="flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
                        >
                            <Download className="h-4 w-4 mr-2" />
                            {t('common.export')}
                        </button>
                    </div>
                </div>

                {/* School Info Card */}
                {school && (
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6"
                    >
                        <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-4">
                                <div className="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                                    <Building2 className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                                </div>
                                <div>
                                    <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                                        {school.name}
                                    </h2>
                                    <div className="space-y-1 mt-2">
                                        <p className="text-sm text-gray-600 dark:text-gray-400 flex items-center">
                                            <MapPin className="h-4 w-4 mr-2" />
                                            {school.address}
                                        </p>
                                        <p className="text-sm text-gray-600 dark:text-gray-400 flex items-center">
                                            <Mail className="h-4 w-4 mr-2" />
                                            {school.email}
                                        </p>
                                        <p className="text-sm text-gray-600 dark:text-gray-400 flex items-center">
                                            <Phone className="h-4 w-4 mr-2" />
                                            {String(school.phone)}
                                        </p>
                                    </div>
                                </div>
                            </div>
                            {analytics?.subscription && (
                                <div className="text-right">
                                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${getPlanTypeColor(analytics.subscription.plan_type)}`}>
                                        {getPlanTypeText(analytics.subscription.plan_type)}
                                    </span>
                                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
                                        {tDashboard('super-admin', 'credits', 'status')}: <span className="font-medium">{analytics.subscription.status}</span>
                                    </p>
                                </div>
                            )}
                        </div>
                    </motion.div>
                )}

                {/* Analytics Cards */}
                {analytics && (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.1 }}
                            className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 border-l-4 border-green-500"
                        >
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{tDashboard('super-admin', 'credits', 'current_balance')}</p>
                                    <p className="text-3xl font-bold text-green-600 dark:text-green-400">
                                        {formatCredits(analytics.analytics.current_balance)}
                                    </p>
                                </div>
                                <CreditCard className="h-8 w-8 text-green-500" />
                            </div>
                            <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
                                {tDashboard('super-admin', 'credits', 'available_credits')}
                            </p>
                        </motion.div>

                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.2 }}
                            className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 border-l-4 border-blue-500"
                        >
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{tDashboard('super-admin', 'credits', 'total_revenue')}</p>
                                    <p className="text-3xl font-bold text-blue-600 dark:text-blue-400">
                                        {formatCurrency(analytics.analytics.total_revenue)}
                                    </p>
                                </div>
                                <DollarSign className="h-8 w-8 text-blue-500" />
                            </div>
                            <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
                                {tDashboard('super-admin', 'credits', 'since_beginning')}
                            </p>
                        </motion.div>

                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.3 }}
                            className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 border-l-4 border-purple-500"
                        >
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">ARPU</p>
                                    <p className="text-3xl font-bold text-purple-600 dark:text-purple-400">
                                        {formatCurrency(studentStats?.average_payment || 0)}
                                    </p>
                                </div>
                                <Target className="h-8 w-8 text-purple-500" />
                            </div>
                            <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
                                {tDashboard('super-admin', 'credits', 'average_revenue_per_student')}
                            </p>
                        </motion.div>

                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.4 }}
                            className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 border-l-4 border-orange-500"
                        >
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{tDashboard('super-admin', 'credits', 'autonomy')}</p>
                                    <p className="text-3xl font-bold text-orange-600 dark:text-orange-400">
                                        {analytics.analytics.days_remaining}j
                                    </p>
                                </div>
                                <Clock className="h-8 w-8 text-orange-500" />
                            </div>
                            <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
                                {tDashboard('super-admin', 'credits', 'estimated_days_remaining')}
                            </p>
                        </motion.div>
                    </div>
                )}

                {/* Additional Metrics */}
                {analytics && (
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.5 }}
                            className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6"
                        >
                            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                                <BarChart3 className="h-5 w-5 mr-2 text-blue-600" />
                                {tDashboard('super-admin', 'credits', 'usage_metrics')}
                            </h3>
                            <div className="space-y-4">
                                <div className="flex items-center justify-between">
                                    <span className="text-sm text-gray-600 dark:text-gray-400">{tDashboard('super-admin', 'credits', 'efficiency')}</span>
                                    <span className="text-sm font-medium text-gray-900 dark:text-white">
                                        {analytics.analytics.efficiency}%
                                    </span>
                                </div>
                                <div className="flex items-center justify-between">
                                    <span className="text-sm text-gray-600 dark:text-gray-400">{tDashboard('super-admin', 'credits', 'registered_students')}</span>
                                    <span className="text-sm font-medium text-gray-900 dark:text-white">
                                        {studentStats?.registered_students || 0}
                                    </span>
                                </div>
                                <div className="flex items-center justify-between">
                                    <span className="text-sm text-gray-600 dark:text-gray-400">{tDashboard('super-admin', 'credits', 'purchased_credits')}</span>
                                    <span className="text-sm font-medium text-gray-900 dark:text-white">
                                        {formatCredits(analytics.analytics.total_purchased)}
                                    </span>
                                </div>
                                <div className="flex items-center justify-between">
                                    <span className="text-sm text-gray-600 dark:text-gray-400">{tDashboard('super-admin', 'credits', 'purchase_count')}</span>
                                    <span className="text-sm font-medium text-gray-900 dark:text-white">
                                        {analytics.analytics.purchase_count}
                                    </span>
                                </div>
                                <div className="flex items-center justify-between">
                                    <span className="text-sm text-gray-600 dark:text-gray-400">{tDashboard('super-admin', 'credits', 'average_purchase')}</span>
                                    <span className="text-sm font-medium text-gray-900 dark:text-white">
                                        {formatCurrency(analytics.analytics.average_purchase)}
                                    </span>
                                </div>
                            </div>
                        </motion.div>

                        {/* Recent Purchases */}
                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.6 }}
                            className="lg:col-span-2 bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6"
                        >
                            <div className="flex items-center justify-between mb-4">
                                <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                                    <Coins className="h-5 w-5 mr-2 text-yellow-600" />
                                    {tDashboard('super-admin', 'credits', 'recent_transactions')}
                                </h3>
                                <button
                                    onClick={() => router.push(`${BASE_URL}/credit/transactions?school_id=${schoolId}`)}
                                    className="text-sm text-blue-600 hover:text-blue-700 dark:text-blue-400"
                                >
                                    {t('common.view_all')}
                                </button>
                            </div>

                            {loadingPurchases ? (
                                <div className="flex justify-center py-8">
                                    <CircularLoader />
                                </div>
                            ) : purchases.length > 0 ? (
                                <div className="space-y-3">
                                    {purchases.slice(0, 5).map((purchase) => (
                                        <div key={purchase._id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                                            <div className="flex items-center space-x-3">
                                                <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                                                    <CreditCard className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                                                </div>
                                                <div>
                                                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                                                        {formatCredits(purchase.credits_purchased)}
                                                    </p>
                                                    <p className="text-xs text-gray-600 dark:text-gray-400">
                                                        {new Date(purchase.purchase_date).toLocaleDateString('fr-FR')}
                                                    </p>
                                                </div>
                                            </div>
                                            <div className="text-right">
                                                <p className="text-sm font-medium text-gray-900 dark:text-white">
                                                    {formatCurrency(purchase.total_amount)}
                                                </p>
                                                <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getPaymentStatusColor(purchase.payment_status)}`}>
                                                    {getPaymentStatusText(purchase.payment_status)}
                                                </span>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                                    <Coins className="h-8 w-8 mx-auto mb-2 opacity-50" />
                                    <p className="text-sm">{tDashboard('super-admin', 'credits', 'no_transactions_found')}</p>
                                </div>
                            )}
                        </motion.div>
                    </div>
                )}

                {/* Subscription Details */}
                {analytics?.subscription && (
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.7 }}
                        className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6"
                    >
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                            <CheckCircle className="h-5 w-5 mr-2 text-green-600" />
                            {tDashboard('super-admin', 'credits', 'subscription_details')}
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                            <div>
                                <p className="text-sm text-gray-600 dark:text-gray-400">{tDashboard('super-admin', 'credits', 'plan_type')}</p>
                                <span className={`inline-flex px-3 py-1 text-sm font-medium rounded-full ${getPlanTypeColor(analytics.subscription.plan_type)}`}>
                                    {getPlanTypeText(analytics.subscription.plan_type)}
                                </span>
                            </div>
                            <div>
                                <p className="text-sm text-gray-600 dark:text-gray-400">{tDashboard('super-admin', 'credits', 'start_date')}</p>
                                <p className="text-sm font-medium text-gray-900 dark:text-white">
                                    {new Date(analytics.subscription.subscription_start).toLocaleDateString('fr-FR')}
                                </p>
                            </div>
                            <div>
                                <p className="text-sm text-gray-600 dark:text-gray-400">{tDashboard('super-admin', 'subscription-plans', 'features')}</p>
                                <p className="text-sm font-medium text-gray-900 dark:text-white">
                                    {analytics.subscription.features.length} {tDashboard('super-admin', 'credits', 'activated')}
                                </p>
                            </div>
                            <div>
                                <p className="text-sm text-gray-600 dark:text-gray-400">{tDashboard('super-admin', 'credits', 'status')}</p>
                                <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                                    analytics.subscription.status === 'active' ? 'text-green-600 bg-green-100' : 'text-yellow-600 bg-yellow-100'
                                }`}>
                                    {analytics.subscription.status}
                                </span>
                            </div>
                        </div>
                    </motion.div>
                )}
            </div>

            {/* Modal d'export */}
            <ExportReportModal
                isOpen={isExportModalOpen}
                onClose={() => setIsExportModalOpen(false)}
                type="school"
                schoolId={schoolId || undefined}
                schoolName={school?.name}
            />
        </SuperLayout>
    );
}
