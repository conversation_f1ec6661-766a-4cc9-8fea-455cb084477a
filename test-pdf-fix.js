// Script de test pour vérifier les corrections du PDF
const { generatePDFReport } = require('./src/services/reportService');
const fs = require('fs');
const path = require('path');

// Données de test pour une école
const testSchoolData = {
  title: 'Rapport École - Test',
  generated_at: new Date().toISOString(),
  period: 'month',
  school: {
    name: 'École de Test',
    email: '<EMAIL>',
    address: '123 Rue de Test, Ville Test',
    plan_type: 'standard',
    status: 'active'
  },
  summary: {
    credits_balance: 150,
    credits_purchased: 500,
    credits_used: 350,
    total_paid: 1500000,
    efficiency: 70
  },
  purchases: [
    {
      date: new Date('2024-01-15'),
      amount: 300000,
      credits: 100,
      status: 'completed'
    },
    {
      date: new Date('2024-01-10'),
      amount: 600000,
      credits: 200,
      status: 'completed'
    },
    {
      date: new Date('2024-01-05'),
      amount: 450000,
      credits: 150,
      status: 'completed'
    }
  ],
  usage_history: [
    {
      date: new Date('2024-01-18'),
      credits: 5,
      type: 'student_creation',
      description: 'Création d\'un nouvel étudiant'
    },
    {
      date: new Date('2024-01-17'),
      credits: 3,
      type: 'class_management',
      description: 'Gestion de classe'
    },
    {
      date: new Date('2024-01-16'),
      credits: 2,
      type: 'chatbot_usage',
      description: 'Utilisation du chatbot'
    }
  ]
};

// Mock de la réponse pour sauvegarder le PDF dans un fichier
class MockResponse {
  constructor(filename) {
    this.filename = filename;
    this.headers = {};
    this.chunks = [];
  }

  setHeader(key, value) {
    this.headers[key] = value;
    console.log(`Header set: ${key} = ${value}`);
  }

  write(chunk) {
    this.chunks.push(chunk);
  }

  end() {
    const buffer = Buffer.concat(this.chunks);
    fs.writeFileSync(this.filename, buffer);
    console.log(`✅ PDF sauvegardé: ${this.filename} (${buffer.length} bytes)`);
  }

  send(buffer) {
    fs.writeFileSync(this.filename, buffer);
    console.log(`✅ PDF sauvegardé: ${this.filename} (${buffer.length} bytes)`);
  }

  status(code) {
    return {
      json: (data) => console.log(`Status ${code}:`, data)
    };
  }
}

// Fonction de test
async function testPDFGeneration() {
  try {
    console.log('🔄 Test de génération PDF avec corrections...');

    // Créer le répertoire de test s'il n'existe pas
    const testDir = path.join(__dirname, 'test-reports');
    if (!fs.existsSync(testDir)) {
      fs.mkdirSync(testDir);
    }

    // Tester le PDF d'école
    const schoolPdfPath = path.join(testDir, 'test-school-report.pdf');
    const mockRes = new MockResponse(schoolPdfPath);

    await generatePDFReport(mockRes, testSchoolData, 'school');

    console.log('✅ Test de génération PDF terminé avec succès !');
    console.log(`📁 Fichier généré: ${schoolPdfPath}`);
    console.log('🔍 Vérifiez le fichier PDF pour confirmer que:');
    console.log('   - Les caractères s\'affichent correctement');
    console.log('   - Aucune donnée "undefined" n\'apparaît');
    console.log('   - Les montants sont formatés correctement');

  } catch (error) {
    console.error('❌ Erreur lors du test:', error);
  }
}

// Exécuter le test
if (require.main === module) {
  testPDFGeneration();
}

module.exports = { testPDFGeneration };
