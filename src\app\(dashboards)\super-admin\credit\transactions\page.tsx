"use client";

import SuperLayout from "@/components/Dashboard/Layouts/SuperLayout";
import useAuth from "@/app/hooks/useAuth";
import CircularLoader from "@/components/widgets/CircularLoader";
import React, { useEffect, useState } from "react";
import { 
  Coins, 
  ArrowLeft, 
  CreditCard, 
  Download,
  RefreshCw,
  Search,
  Filter,
  Calendar,
  ChevronLeft,
  ChevronRight
} from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import { getSchoolBy_id } from "@/app/services/SchoolServices";
import { SchoolSchema } from "@/app/models/SchoolModel";
import { motion } from "framer-motion";
import { 
  getCreditPurchaseHistory,
  formatCurrency, 
  formatCredits,
  getPaymentStatusText,
  getPaymentStatusColor,
  CreditPurchaseSchema
} from "@/app/services/CreditPurchaseServices";
import { useTranslation } from '@/hooks/useTranslation';

export default function SchoolTransactionsPage() {
    const { t, tDashboard } = useTranslation();
    const BASE_URL = "/super-admin";
    const { user, logout } = useAuth();
    const router = useRouter();
    const searchParams = useSearchParams();
    const schoolId = searchParams.get('school_id');

    const [school, setSchool] = useState<SchoolSchema | null>(null);
    const [transactions, setTransactions] = useState<CreditPurchaseSchema[]>([]);
    const [loading, setLoading] = useState(true);
    const [loadingMore, setLoadingMore] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [searchTerm, setSearchTerm] = useState('');
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const [hasMore, setHasMore] = useState(false);
    
    const ITEMS_PER_PAGE = 20;

    const navigation = {
        icon: Coins,
        baseHref: `${BASE_URL}/credit/transactions`,
        title: tDashboard('super-admin', 'credits', 'all_transactions'),
    };

    useEffect(() => {
        if (schoolId) {
            fetchSchoolData();
            fetchTransactions(1);
        }
    }, [schoolId]);

    const fetchSchoolData = async () => {
        try {
            const schoolData = await getSchoolBy_id(schoolId!);
            setSchool(schoolData);
        } catch (err: any) {
            setError(err.message || t('messages.error.loading_data'));
        }
    };

    const fetchTransactions = async (page: number = 1, append: boolean = false) => {
        try {
            if (!append) setLoading(true);
            else setLoadingMore(true);
            
            const skip = (page - 1) * ITEMS_PER_PAGE;
            const response = await fetch(
                `/api/credit-purchase/school/${schoolId}/history?limit=${ITEMS_PER_PAGE}&skip=${skip}`,
                {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('idToken')}`
                    }
                }
            );

            if (!response.ok) {
                throw new Error('Failed to fetch transactions');
            }

            const data = await response.json();
            
            if (append) {
                setTransactions(prev => [...prev, ...data.purchases]);
            } else {
                setTransactions(data.purchases || []);
            }
            
            setHasMore(data.pagination?.has_more || false);
            setTotalPages(Math.ceil((data.pagination?.total || 0) / ITEMS_PER_PAGE));
            setCurrentPage(page);

        } catch (err: any) {
            setError(err.message || t('messages.error.loading_data'));
        } finally {
            setLoading(false);
            setLoadingMore(false);
        }
    };

    const handleRefresh = () => {
        fetchTransactions(1);
    };

    const handleLoadMore = () => {
        if (hasMore && !loadingMore) {
            fetchTransactions(currentPage + 1, true);
        }
    };

    const filteredTransactions = transactions.filter(transaction =>
        transaction.purchase_id?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        formatCurrency(transaction.total_amount).toLowerCase().includes(searchTerm.toLowerCase()) ||
        getPaymentStatusText(transaction.payment_status).toLowerCase().includes(searchTerm.toLowerCase())
    );

    if (!schoolId) {
        return (
            <SuperLayout navigation={navigation} user={user} logout={logout}>
                <div className="flex items-center justify-center min-h-screen">
                    <div className="text-center">
                        <p className="text-gray-500 dark:text-gray-400">{t('messages.error.school_id_required')}</p>
                        <button
                            onClick={() => router.push(`${BASE_URL}/credit`)}
                            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                        >
                            {t('common.go_back')}
                        </button>
                    </div>
                </div>
            </SuperLayout>
        );
    }

    return (
        <SuperLayout navigation={navigation} user={user} logout={logout}>
            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                        <button
                            onClick={() => router.push(`${BASE_URL}/credit/manage?id=${schoolId}`)}
                            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                        >
                            <ArrowLeft className="h-5 w-5" />
                        </button>
                        <div>
                            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                                {tDashboard('super-admin', 'credits', 'all_transactions')}
                            </h1>
                            {school && (
                                <p className="text-gray-600 dark:text-gray-400 mt-1">
                                    {school.name}
                                </p>
                            )}
                        </div>
                    </div>
                    
                    <div className="flex items-center space-x-3">
                        <button
                            onClick={handleRefresh}
                            disabled={loading}
                            className="flex items-center px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors disabled:opacity-50"
                        >
                            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                            {t('common.refresh')}
                        </button>
                    </div>
                </div>

                {/* Search and Filters */}
                <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
                    <div className="flex flex-col sm:flex-row gap-4">
                        <div className="flex-1">
                            <div className="relative">
                                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                                <input
                                    type="text"
                                    placeholder={t('common.search_placeholder')}
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                                />
                            </div>
                        </div>
                    </div>
                </div>

                {/* Transactions List */}
                {loading ? (
                    <div className="flex justify-center py-12">
                        <CircularLoader />
                    </div>
                ) : error ? (
                    <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl p-6 text-center">
                        <p className="text-red-700 dark:text-red-300">{error}</p>
                        <button
                            onClick={handleRefresh}
                            className="mt-4 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
                        >
                            {t('common.retry')}
                        </button>
                    </div>
                ) : (
                    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden">
                        {filteredTransactions.length > 0 ? (
                            <>
                                <div className="overflow-x-auto">
                                    <table className="w-full">
                                        <thead className="bg-gray-50 dark:bg-gray-700">
                                            <tr>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                                    {t('common.transaction_id')}
                                                </th>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                                    {t('common.date')}
                                                </th>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                                    {t('common.credits')}
                                                </th>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                                    {t('common.amount')}
                                                </th>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                                    {t('common.status')}
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                            {filteredTransactions.map((transaction) => (
                                                <tr key={transaction._id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <div className="flex items-center">
                                                            <CreditCard className="h-4 w-4 text-blue-500 mr-2" />
                                                            <span className="text-sm font-medium text-gray-900 dark:text-white">
                                                                {transaction.purchase_id || transaction._id}
                                                            </span>
                                                        </div>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400">
                                                        {new Date(transaction.purchase_date).toLocaleDateString('fr-FR')}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                                                        {formatCredits(transaction.credits_purchased)}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                                                        {formatCurrency(transaction.total_amount)}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getPaymentStatusColor(transaction.payment_status)}`}>
                                                            {getPaymentStatusText(transaction.payment_status)}
                                                        </span>
                                                    </td>
                                                </tr>
                                            ))}
                                        </tbody>
                                    </table>
                                </div>
                                
                                {/* Load More Button */}
                                {hasMore && (
                                    <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
                                        <button
                                            onClick={handleLoadMore}
                                            disabled={loadingMore}
                                            className="w-full flex items-center justify-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors disabled:opacity-50"
                                        >
                                            {loadingMore ? (
                                                <CircularLoader />
                                            ) : (
                                                <>
                                                    <Download className="h-4 w-4 mr-2" />
                                                    {t('common.load_more')}
                                                </>
                                            )}
                                        </button>
                                    </div>
                                )}
                            </>
                        ) : (
                            <div className="text-center py-12">
                                <Coins className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                                <p className="text-gray-500 dark:text-gray-400">
                                    {searchTerm ? t('common.no_results_found') : tDashboard('super-admin', 'credits', 'no_transactions_found')}
                                </p>
                            </div>
                        )}
                    </div>
                )}
            </div>
        </SuperLayout>
    );
}
