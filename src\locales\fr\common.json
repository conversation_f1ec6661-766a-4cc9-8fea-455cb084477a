{"common": {"loading": "Chargement...", "save": "Enregistrer", "cancel": "Annuler", "delete": "<PERSON><PERSON><PERSON><PERSON>", "edit": "Modifier", "add": "Ajouter", "search": "<PERSON><PERSON><PERSON>", "filter": "<PERSON><PERSON><PERSON>", "export": "Exporter", "import": "Importer", "refresh": "Actualiser", "back": "Retour", "next": "Suivant", "previous": "Précédent", "submit": "So<PERSON><PERSON><PERSON>", "confirm": "Confirmer", "yes": "O<PERSON>", "no": "Non", "close": "<PERSON><PERSON><PERSON>", "open": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "view": "Voir", "download": "Télécharger", "upload": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "clear": "<PERSON><PERSON><PERSON><PERSON>", "reset": "Réinitialiser", "apply": "Appliquer", "actions": "Actions", "status": "Statut", "active": "Actif", "inactive": "Inactif", "enabled": "Activé", "disabled": "Désactivé", "success": "Su<PERSON>ès", "error": "<PERSON><PERSON><PERSON>", "warning": "Avertissement", "info": "Information", "required": "Requis", "optional": "Optionnel", "name": "Nom", "email": "Email", "phone": "Téléphone", "address": "<PERSON><PERSON><PERSON>", "date": "Date", "time": "<PERSON><PERSON>", "description": "Description", "total": "Total", "amount": "<PERSON><PERSON>", "price": "Prix", "quantity": "Quantité", "example_title": "Exemple: {title}", "unknown": "Inconnu", "deleting": "Suppression en cours...", "continue": "<PERSON><PERSON><PERSON>", "notification": "Notification"}, "navigation": {"dashboard": "Tableau de bord", "schools": "Écoles", "users": "Utilisateurs", "students": "Étudiants", "teachers": "Enseignants", "parents": "Parents", "classes": "Classes", "subjects": "<PERSON><PERSON><PERSON>", "grades": "Notes", "attendance": "Présence", "schedule": "Emploi du temps", "reports": "Rapports", "settings": "Paramètres", "profile": "Profil", "logout": "Déconnexion", "notifications": "Notifications", "credits": "Crédits", "subscriptions": "Abonnements", "payments": "Paiements", "analytics": "Analyses"}, "dashboard": {"super-admin": {"pages": {"dashboard": {"title": "Tableau de bord Super Admin", "welcome": "Bienvenue, {name}", "overview": "Vue d'ensemble", "statistics": "Statistiques", "recent_activities": "Activités récentes", "quick_actions": "Actions rapides", "total_schools": "Total des écoles", "total_students": "Total des étudiants", "total_teachers": "Total des enseignants", "total_users": "Total des utilisateurs", "active_subscriptions": "Abonnements actifs", "revenue": "<PERSON><PERSON><PERSON> (XAF)", "growth": "Croissance"}, "schools": {"title": "Gestion des écoles", "add_school": "Ajouter une école", "edit_school": "Modifier l'école", "school_name": "Nom de l'école", "school_code": "Code de l'école", "school_type": "Type d'école", "contact_person": "<PERSON><PERSON> de contact", "subscription_plan": "Plan d'abonnement", "registration_date": "Date d'inscription", "last_activity": "Dernière activité", "view_details": "Voir les détails", "manage_credits": "<PERSON><PERSON><PERSON> les crédits", "view_analytics": "Voir les analyses", "principal_name": "Nom du directeur", "established_year": "Année de <PERSON>réation", "website": "Site web"}, "credits": {"title": "Gestion des crédits", "available_credits": "Crédits disponibles", "used_credits": "Crédits utilisés", "total_credits": "Total des crédits", "credit_history": "Historique des crédits", "purchase_credits": "Acheter des crédits", "credit_usage": "Utilisation des crédits", "remaining_credits": "Crédits restants", "credit_balance": "Solde de crédits", "per_student": "par étudiant", "per_message": "par message", "overview": "Vue d'ensemble des achats de crédits et analyses par école", "search_placeholder": "Rechercher une école par nom ou adresse..."}, "subscriptions": {"title": "Gestion des abonnements", "plan_basic": "Basique", "plan_standard": "Standard", "plan_custom": "<PERSON><PERSON><PERSON><PERSON>", "plan_features": "Fonctionnalités du plan", "subscription_status": "Statut de l'abonnement", "renewal_date": "Date de renouvellement", "upgrade_plan": "Mettre à niveau le plan", "downgrade_plan": "Rétrograder le plan", "cancel_subscription": "Annuler l'abonnement"}, "reports": {"title": "Rapports", "generate_report": "Générer un rapport", "export_data": "Exporter les données", "date_range": "Plage de dates", "report_type": "Type de rapport"}, "users": {"title": "Gestion des utilisateurs", "add_user": "Ajouter un utilisateur", "edit_user": "Modifier l'utilisateur", "user_role": "Rôle de l'utilisateur", "user_status": "Statut de l'utilisateur", "last_login": "Dernière connexion", "account_created": "<PERSON><PERSON><PERSON>", "all_roles": "To<PERSON> les rôles", "filter_by_role": "Filtrer par rôle", "user_details": "Détails de l'utilisateur", "delete_user": "Supprimer l'utilisateur", "bulk_delete": "Suppression en lot", "selected_users": "Utilisateurs sélectionnés"}, "students": {"title": "Gestion des étudiants", "add_student": "Ajouter un étudiant", "edit_student": "Modifier l'étudiant", "student_details": "Détails de l'étudiant", "enrollment_status": "Statut d'inscription", "academic_year": "<PERSON><PERSON> acadé<PERSON>", "class_level": "Niveau de classe"}, "classes": {"title": "Gestion des classes", "add_class": "Ajouter une classe", "edit_class": "Modifier la classe", "class_details": "Détails de la classe", "class_capacity": "Capacité de la classe", "assigned_teacher": "Enseignant assigné"}, "parents": {"title": "Gestion des parents", "invite_parent": "Inviter un parent", "parent_details": "<PERSON><PERSON><PERSON> du parent", "children": "<PERSON><PERSON><PERSON>", "contact_info": "Informations de contact"}, "refunds": {"title": "Gestion des remboursements", "refund_request": "<PERSON><PERSON><PERSON> de rembo<PERSON>", "refund_status": "Statut du remboursement", "refund_amount": "Montant du remboursement", "process_refund": "Trai<PERSON> le remboursement", "refund_reason": "Raison du remboursement"}, "subscription-plans": {"title": "Plans d'abonnement", "create_plan": "Créer un plan", "edit_plan": "Modifier le plan", "plan_details": "<PERSON>é<PERSON> du plan", "plan_features": "Fonctionnalités du plan", "plan_pricing": "Tarification du plan", "active_plans": "Plans actifs"}, "settings": {"title": "Paramètres", "general_settings": "Paramètres généraux", "system_settings": "Paramètres système", "notification_settings": "Paramètres de notification", "security_settings": "Paramètres de sécurité", "backup_settings": "Paramètres de sauvegarde"}}}, "school-admin": {"pages": {"dashboard": {"title": "Tableau de bord École", "welcome": "Bienvenue, {name}", "overview": "Vue d'ensemble de l'école", "statistics": "Statistiques de l'école", "recent_activities": "Activités récentes", "quick_actions": "Actions rapides", "total_students": "Total des étudiants", "total_teachers": "Total des enseignants", "total_classes": "Total des classes", "attendance_rate": "<PERSON>x de présence", "academic_performance": "Performance académique"}, "students": {"title": "Gestion des étudiants", "add_student": "Ajouter un étudiant", "edit_student": "Modifier l'étudiant", "student_id": "ID étudiant", "student_name": "Nom de l'étudiant", "class_level": "Niveau de classe", "enrollment_date": "Date d'inscription", "parent_contact": "Contact parent", "academic_year": "<PERSON><PERSON> acadé<PERSON>"}, "teachers": {"title": "Gestion des enseignants", "add_teacher": "Ajouter un enseignant", "edit_teacher": "Modifier l'enseignant", "teacher_id": "ID enseignant", "teacher_name": "Nom de l'enseignant", "subject_taught": "<PERSON><PERSON> enseign<PERSON>", "hire_date": "Date d'embauche", "qualification": "Qualification"}, "classes": {"title": "Gestion des classes", "add_class": "Ajouter une classe", "edit_class": "Modifier la classe", "class_name": "Nom de la classe", "class_capacity": "Capacité de la classe", "assigned_teacher": "Enseignant assigné", "schedule": "Emploi du temps"}, "attendance": {"title": "Gestion des présences", "mark_attendance": "Marquer la présence", "attendance_report": "Rapport de présence", "present": "Présent", "absent": "Absent", "late": "En retard", "excused": "Excusé"}, "grades": {"title": "Gestion des notes", "add_grade": "Ajouter une note", "edit_grade": "Modifier la note", "grade_book": "Carnet de notes", "subject": "<PERSON><PERSON>", "exam_type": "Type d'examen", "score": "Score"}}}, "teacher-dashboard": {"pages": {"dashboard": {"title": "<PERSON><PERSON> <PERSON> b<PERSON> En<PERSON>gnant", "welcome": "Bienvenue, {name}", "overview": "Vue d'ensemble", "my_classes": "Mes classes", "today_schedule": "Emploi du temps d'aujourd'hui", "recent_activities": "Activités récentes", "quick_actions": "Actions rapides", "students_count": "Nombre d'étudiants", "subjects_taught": "Matières enseignées", "upcoming_classes": "Cours à venir"}, "classes": {"title": "Mes classes", "class_details": "Détails de la classe", "student_list": "Liste des étudiants", "class_schedule": "Emploi du temps de la classe", "class_performance": "Performance de la classe"}, "attendance": {"title": "Présences", "take_attendance": "<PERSON><PERSON><PERSON> les présences", "attendance_history": "Historique des présences", "mark_present": "Marquer présent", "mark_absent": "<PERSON><PERSON> absent", "attendance_summary": "Résumé des présences"}, "grades": {"title": "Notes", "enter_grades": "Sai<PERSON> les notes", "grade_history": "Historique des notes", "grade_statistics": "Statistiques des notes", "class_average": "Moyenne de la classe", "student_progress": "Progrès de l'étudiant"}, "resources": {"title": "Ressources", "upload_resource": "<PERSON><PERSON><PERSON><PERSON>r une ressource", "my_resources": "<PERSON><PERSON> ressources", "shared_resources": "Ressources partagées", "resource_library": "Bibliothèque de ressources"}}}}, "components": {"chart": {"user_growth_trend": "Tendance de croissance des utilisateurs", "loading_user_stats": "Chargement des statistiques utilisateur...", "failed_to_load_data": "Échec du chargement des données", "select_year": "Sélectionner l'année"}, "performance_table": {"title": "Performance des écoles", "school_name": "Nom de l'école", "metric": "Métrique", "value": "<PERSON><PERSON>", "search_placeholder": "Rechercher une école...", "items_per_page": "Éléments par page", "showing": "Affichage de", "to": "à", "of": "sur", "entries": "entrées", "previous": "Précédent", "next": "Suivant", "no_data": "<PERSON><PERSON><PERSON> donnée disponible", "select_metric": "Sélectionner une métrique"}, "data_table": {"search": "Rechercher...", "filter": "<PERSON><PERSON><PERSON>", "export": "Exporter", "bulk_actions": "Actions en lot", "select_all": "<PERSON><PERSON>", "deselect_all": "<PERSON><PERSON>", "delete_selected": "Supprimer la sélection", "no_results": "Aucun résultat trouvé", "loading": "Chargement...", "rows_per_page": "Lignes par page", "page": "Page", "of_pages": "sur {total} pages", "no_data": "<PERSON><PERSON><PERSON> donnée disponible", "delete_selected_one": "Supprimer la sélection (1)", "delete_selected_count": "Supprimer la sélection ({count})", "view_details": "Voir les détails", "selected_count": "{selected} sur {total} sélectionnés", "select_all_count": "<PERSON><PERSON> s<PERSON> ({count})", "delete_all_count": "Tout supprimer ({count})", "active_filters": "Filtres actifs", "page_of": "Page {current} sur {total}", "items_per_page": "Éléments par page", "actions": "Actions"}, "modals": {"create": "<PERSON><PERSON><PERSON>", "edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>", "confirm": "Confirmer", "cancel": "Annuler", "save": "Enregistrer", "close": "<PERSON><PERSON><PERSON>", "are_you_sure": "Êtes-vous sûr ?", "this_action_cannot_be_undone": "Cette action ne peut pas être annulée", "delete_confirmation": "Êtes-vous sûr de vouloir supprimer cet élément ?", "delete_confirmation_with_name": "Êtes-vous sûr de vouloir supprimer <strong class=\"font-semibold text-pink-500\">{name}?</strong>", "bulk_delete_confirmation": "Êtes-vous sûr de vouloir supprimer {count} éléments sélectionnés ?", "delete_all_items": "Supprimer tous les {type}", "delete_selected_items": "Supprimer les {type} sélectionnés", "delete_all_warning": "Ceci supprimera définitivement TOUS les {type} du système. Cette action ne peut pas être annulée !", "delete_selected_warning": "Ceci supprimera définitivement les {type} sélectionnés. Cette action ne peut pas être annulée !", "danger_delete_all": "⚠️ DANGER : Opération de suppression totale", "bulk_delete_operation": "⚠️ Opération de suppression en lot", "about_to_delete": "Vous êtes sur le point de supprimer <strong class=\"text-red-600\">{count}</strong> {type}.", "delete_all_system_warning": "Ceci supprimera TOUS les {type} du système et ne peut pas être annulé !", "enter_password_to_confirm": "Veuillez entrer votre mot de passe pour confirmer cette opération {type} :", "destructive": "destructrice", "bulk": "en lot", "delete_count_items": "Supprimer {count} {type}"}}, "notifications": {"title": "Notifications", "mark_as_read": "Marquer comme lu", "mark_all_read": "Tout marquer comme lu", "no_notifications": "Aucune notification", "new_notification": "Nouvelle notification", "notification_settings": "Paramètres de notification", "email_notifications": "Notifications par email", "push_notifications": "Notifications push"}, "forms": {"validation": {"required_field": "Ce champ est requis", "invalid_email": "<PERSON><PERSON><PERSON> email invalide", "invalid_phone": "Numéro de téléphone invalide", "password_too_short": "Le mot de passe doit contenir au moins 8 caractères", "passwords_dont_match": "Les mots de passe ne correspondent pas", "invalid_format": "Format invalide", "select_at_least_one": "Veuillez sélectionner au moins un élément à supprimer", "no_items_to_delete": "Aucun élément à supprimer", "password_required": "Veuillez entrer votre mot de passe pour confirmer la suppression"}, "placeholders": {"enter_name": "Entrez le nom", "enter_email": "Entrez l'email", "enter_phone": "Entrez le téléphone", "enter_address": "Entrez l'adresse", "select_option": "Sélectionnez une option", "search_placeholder": "Rechercher...", "password": "Mot de passe", "confirm_password": "Confirmer le mot de passe", "full_name": "Nom complet", "role": "R<PERSON><PERSON>", "select_role": "Sélectionner un rôle", "select_school": "Sélectionner une école", "website": "Site web", "principal_name": "Nom du directeur", "established_year": "Année de <PERSON>réation", "password_confirm_delete": "Tapez votre mot de passe pour confirmer la suppression"}}, "messages": {"success": {"saved": "Enregistré avec succès", "deleted": "Supprimé avec succès", "updated": "Mis à jour avec succès", "created": "<PERSON><PERSON><PERSON> avec succès", "sent": "<PERSON><PERSON><PERSON> a<PERSON> su<PERSON>", "all_items_deleted": "Tous les {type} ont été supprimés avec succès", "items_deleted": "{count} {type} supprimé(s) avec succès", "bulk_delete": "Suppression en lot réussie !"}, "error": {"generic": "Une erreur s'est produite", "network": "<PERSON><PERSON><PERSON> <PERSON>", "unauthorized": "Non autorisé", "forbidden": "Accès interdit", "not_found": "Non trouvé", "server_error": "<PERSON><PERSON><PERSON> du <PERSON>", "invalid_password": "Mot de passe invalide !", "bulk_delete_failed": "La suppression en lot a échoué. Veuillez réessayer."}, "confirmation": {"delete": "Êtes-vous sûr de vouloir supprimer cet élément ?", "cancel": "Êtes-vous sûr de vouloir annuler ?", "logout": "Êtes-vous sûr de vouloir vous déconnecter ?"}}, "language": {"french": "Français", "english": "<PERSON><PERSON><PERSON>", "select_language": "Sélectionner la langue", "language_changed": "Langue changée avec succès"}}