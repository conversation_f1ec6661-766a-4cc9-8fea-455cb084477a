{"common": {"loading": "Chargement...", "save": "Enregistrer", "cancel": "Annuler", "delete": "<PERSON><PERSON><PERSON><PERSON>", "edit": "Modifier", "add": "Ajouter", "search": "<PERSON><PERSON><PERSON>", "filter": "<PERSON><PERSON><PERSON>", "export": "Exporter", "import": "Importer", "refresh": "Actualiser", "back": "Retour", "next": "Suivant", "previous": "Précédent", "submit": "So<PERSON><PERSON><PERSON>", "confirm": "Confirmer", "yes": "O<PERSON>", "no": "Non", "close": "<PERSON><PERSON><PERSON>", "open": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "view": "Voir", "download": "Télécharger", "upload": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "clear": "<PERSON><PERSON><PERSON><PERSON>", "reset": "Réinitialiser", "apply": "Appliquer", "actions": "Actions", "status": "Statut", "active": "Actif", "inactive": "Inactif", "enabled": "Activé", "disabled": "Désactivé", "success": "Su<PERSON>ès", "error": "<PERSON><PERSON><PERSON>", "warning": "Avertissement", "info": "Information", "required": "Requis", "optional": "Optionnel", "name": "Nom", "email": "Email", "phone": "Téléphone", "address": "<PERSON><PERSON><PERSON>", "date": "Date", "time": "<PERSON><PERSON>", "description": "Description", "total": "Total", "amount": "<PERSON><PERSON>", "price": "Prix", "quantity": "Quantité", "example_title": "Exemple: {title}", "unknown": "Inconnu", "deleting": "Suppression en cours...", "continue": "<PERSON><PERSON><PERSON>", "notification": "Notification", "update": "Mettre à jour", "create": "<PERSON><PERSON><PERSON>", "retry": "<PERSON><PERSON><PERSON><PERSON>", "sending": "Envoi en cours...", "view_all": "Voir tout", "manage": "<PERSON><PERSON><PERSON>", "not_available": "N/A", "gender": "Genre", "mandatory": "obligatoire"}, "navigation": {"dashboard": "Tableau de bord", "schools": "Écoles", "users": "Utilisateurs", "students": "Étudiants", "teachers": "Enseignants", "parents": "Parents", "classes": "Classes", "subjects": "<PERSON><PERSON><PERSON>", "grades": "Notes", "attendance": "Présence", "schedule": "Emploi du temps", "reports": "Rapports", "settings": "Paramètres", "profile": "Profil", "logout": "Déconnexion", "notifications": "Notifications", "credits": "Crédits", "subscriptions": "Abonnements", "payments": "Paiements", "analytics": "Analyses", "refunds": "Remboursements"}, "dashboard": {"super-admin": {"pages": {"dashboard": {"title": "Tableau de bord Super Admin", "welcome": "Bienvenue, {name}", "overview": "Vue d'ensemble", "statistics": "Statistiques", "recent_activities": "Activités récentes", "quick_actions": "Actions rapides", "total_schools": "Total des écoles", "total_students": "Total des étudiants", "total_teachers": "Total des enseignants", "total_users": "Total des utilisateurs", "active_subscriptions": "Abonnements actifs", "revenue": "<PERSON><PERSON><PERSON> (XAF)", "growth": "Croissance"}, "schools": {"title": "Gestion des écoles", "add_school": "Ajouter une école", "edit_school": "Modifier l'école", "school_name": "Nom de l'école", "school_code": "Code de l'école", "school_type": "Type d'école", "contact_person": "<PERSON><PERSON> de contact", "subscription_plan": "Plan d'abonnement", "registration_date": "Date d'inscription", "last_activity": "Dernière activité", "view_details": "Voir les détails", "manage_credits": "<PERSON><PERSON><PERSON> les crédits", "view_analytics": "Voir les analyses", "principal_name": "Nom du directeur", "established_year": "Année de <PERSON>réation", "website": "Site web"}, "credits": {"title": "Gestion des Crédits", "available_credits": "Crédits disponibles", "used_credits": "Crédits utilisés", "total_credits": "Total des crédits", "credit_history": "Historique des crédits", "purchase_credits": "Acheter des crédits", "credit_usage": "Utilisation des crédits", "remaining_credits": "Crédits restants", "credit_balance": "Solde de crédits", "per_student": "par étudiant", "per_message": "par message", "overview": "Vue d'ensemble des crédits et analytics par école", "search_placeholder": "Rechercher par nom d'école ou adresse...", "balance": "Solde", "revenue": "<PERSON>en<PERSON>", "efficiency": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "remaining": "Restants", "view_details": "Voir Détails", "manage_title": "Détails des Crédits", "manage_description": "Gestion des crédits et analytics détaillées", "missing_school_id": "ID d'école manquant", "select_school_message": "Veuillez sélectionner une école pour voir ses détails.", "back_to_list": "Retour à la liste", "status": "Statut", "current_balance": "Solde Actuel", "since_beginning": "<PERSON><PERSON><PERSON> le dé<PERSON>", "average_revenue_per_student": "Revenu moyen par étudiant", "autonomy": "Autonomie", "estimated_days_remaining": "Jours restants estimés", "usage_metrics": "Métriques d'Utilisation", "registered_students": "Étudiants Enregistrés", "purchased_credits": "Cré<PERSON><PERSON>", "purchase_count": "Nombre d'Achats", "average_purchase": "<PERSON><PERSON><PERSON>", "recent_transactions": "Transactions Récentes", "no_transactions_found": "Aucune transaction trouvée", "subscription_details": "Détails de la Souscription", "plan_type": "Type de Plan", "start_date": "Date de Début", "activated": "activées", "new_transaction": "Nouvelle Transaction de Crédit", "academic_year": "<PERSON><PERSON>", "select_academic_year": "Sélectionner l'année académique", "payment_method": "Méthode de Paiement", "amount_paid": "<PERSON><PERSON>", "send_credit": "Envoyer <PERSON><PERSON>", "total_revenue": "<PERSON><PERSON><PERSON>"}, "subscriptions": {"title": "Gestion des abonnements", "plan_basic": "Basique", "plan_standard": "Standard", "plan_custom": "<PERSON><PERSON><PERSON><PERSON>", "plan_features": "Fonctionnalités du plan", "subscription_status": "Statut de l'abonnement", "renewal_date": "Date de renouvellement", "upgrade_plan": "Mettre à niveau le plan", "downgrade_plan": "Rétrograder le plan", "cancel_subscription": "Annuler l'abonnement"}, "reports": {"title": "Rapports", "generate_report": "Générer un rapport", "export_data": "Exporter les données", "date_range": "Plage de dates", "report_type": "Type de rapport"}, "users": {"title": "Gestion des utilisateurs", "add_user": "Ajouter un utilisateur", "edit_user": "Modifier l'utilisateur", "user_role": "Rôle de l'utilisateur", "user_status": "Statut de l'utilisateur", "last_login": "Dernière connexion", "account_created": "<PERSON><PERSON><PERSON>", "all_roles": "To<PERSON> les rôles", "filter_by_role": "Filtrer par rôle", "user_details": "Détails de l'utilisateur", "delete_user": "Supprimer l'utilisateur", "bulk_delete": "Suppression en lot", "selected_users": "Utilisateurs sélectionnés"}, "parents": {"title": "Gestion des parents", "invite_parent": "Inviter un parent", "parent_details": "<PERSON><PERSON><PERSON> du parent", "children": "<PERSON><PERSON><PERSON>", "contact_info": "Informations de contact"}, "refunds": {"title": "Gestion des remboursements", "refund_request": "<PERSON><PERSON><PERSON> de rembo<PERSON>", "refund_status": "Statut du remboursement", "refund_amount": "Montant du remboursement", "process_refund": "Trai<PERSON> le remboursement", "refund_reason": "Raison du remboursement"}, "subscription-plans": {"title": "Plans d'abonnement", "description": "<PERSON><PERSON><PERSON> les plans d'abonnement et leurs fonctionnalités", "create_plan": "Créer un plan", "edit_plan": "Modifier le plan", "plan_details": "<PERSON>é<PERSON> du plan", "plan_features": "Fonctionnalités du plan", "plan_pricing": "Tarification du plan", "active_plans": "Plans actifs", "new_plan": "Nouveau Plan", "search_placeholder": "Rechercher par nom, description...", "all_plans": "Tous les plans", "active": "Actifs", "inactive": "Inactifs", "popular": "Populaires", "total_plans": "Total des plans", "popular_plans": "Plans populaires", "with_chatbot": "Avec chatbot", "no_plans_found": "Aucun plan trouvé", "no_plans_match_criteria": "Aucun plan ne correspond à vos critères de recherche.", "create_first_plan": "Commencez par créer votre premier plan de souscription.", "min": "Min", "max": "Max", "chatbot": "<PERSON><PERSON><PERSON>", "features": "fonctionnalités", "order": "Ordre", "basic_info": "Informations de base", "display_name": "Nom d'affichage", "display_name_placeholder": "Plan Basic", "technical_name": "Nom technique", "technical_name_placeholder": "basic (auto-généré si vide)", "description_placeholder": "Description du plan...", "pricing": "Tarification", "price_per_credit": "Prix par crédit (XAF)", "minimum_purchase": "Achat minimum", "maximum_purchase": "Achat maximum", "unlimited": "Illimité", "enable_chatbot": "<PERSON><PERSON> le chatbot pour ce plan", "chatbot_credits_per_purchase": "Cré<PERSON>s chatbot par achat", "feature_placeholder": "Fonctionnalité...", "options": "Options", "recommended_for": "Recommandé pour", "recommended_for_placeholder": "Petites écoles (1-100 étudiants)", "display_order": "Ordre d'affichage", "active_plan": "Plan actif", "popular_plan": "Plan populaire", "contact_required": "Contact requis", "updating_plan": "Mise à jour du plan...", "creating_plan": "Création du plan...", "plan_updated": "Plan mis à jour !", "plan_created": "Plan créé !", "plan_updated_success": "Le plan de souscription a été mis à jour avec succès.", "plan_created_success": "Le plan de souscription a été créé avec succès."}, "classes": {"title": "Gestion des Classes", "search_placeholder": "Rechercher par nom d'école ou adresse...", "no_schools_found": "Aucune école trouvée correspondant à vos critères de recherche.", "manage_classes": "<PERSON><PERSON><PERSON> les Classes", "manage_classes_of": "<PERSON><PERSON>rer les Classes de", "class_level": "Niveau de Classe", "class_name": "Nom de la Classe", "class_code": "Code de la Classe", "class_level_name": "Nom du Niveau de Classe", "add_new_class": "Ajouter une Nouvelle Classe", "add_new_level": "Ajouter un Nouveau Niveau", "all_class_levels": "Tous les Niveaux de Classe", "edit_class": "Modifier la Classe", "edit_level": "Modifier le Niveau", "select_level": "Sélectionner un niveau", "level_name_required": "Le nom du niveau de classe est obligatoire."}, "students": {"title": "Gestion des Étudiants", "school_id": "ID École", "school_name": "Nom de l'École", "student_id": "ID Étudiant", "student_name": "Nom de l'Étudiant", "birthday": "Date de Naissance", "place_of_birth": "<PERSON><PERSON> de Naissance", "class_level": "Niveau de Classe", "parent_name": "Nom du Parent", "registered": "Inscrit", "no_class": "Aucune classe", "no_guardian": "<PERSON><PERSON><PERSON> tuteur", "manage_students": "<PERSON><PERSON><PERSON> les Étudiants", "manage_students_of": "G<PERSON>rer les Étudiants de", "popup_blocked_message": "Popup bloqué ! Veuillez autoriser les popups pour voir la liste des étudiants.", "import_complete": "Import terminé : {successful}/{total} étudiants importés.", "register_student": "Inscrire un Étudiant", "upload_csv": "Télécharger Liste CSV", "print_student_list": "Imprimer Liste des Étudiants", "print_id_cards": "Imprimer Cartes d'Identité"}, "settings": {"title": "Paramètres", "general_settings": "Paramètres généraux", "system_settings": "Paramètres système", "notification_settings": "Paramètres de notification", "security_settings": "Paramètres de sécurité", "backup_settings": "Paramètres de sauvegarde"}}}, "school-admin": {"pages": {"dashboard": {"title": "Tableau de bord École", "welcome": "Bienvenue, {name}", "overview": "Vue d'ensemble de l'école", "statistics": "Statistiques de l'école", "recent_activities": "Activités récentes", "quick_actions": "Actions rapides", "total_students": "Total des étudiants", "total_teachers": "Total des enseignants", "total_classes": "Total des classes", "attendance_rate": "<PERSON>x de présence", "academic_performance": "Performance académique"}, "students": {"title": "Gestion des étudiants", "add_student": "Ajouter un étudiant", "edit_student": "Modifier l'étudiant", "student_id": "ID étudiant", "student_name": "Nom de l'étudiant", "class_level": "Niveau de classe", "enrollment_date": "Date d'inscription", "parent_contact": "Contact parent", "academic_year": "<PERSON><PERSON> acadé<PERSON>"}, "teachers": {"title": "Gestion des enseignants", "add_teacher": "Ajouter un enseignant", "edit_teacher": "Modifier l'enseignant", "teacher_id": "ID enseignant", "teacher_name": "Nom de l'enseignant", "subject_taught": "<PERSON><PERSON> enseign<PERSON>", "hire_date": "Date d'embauche", "qualification": "Qualification"}, "classes": {"title": "Gestion des classes", "add_class": "Ajouter une classe", "edit_class": "Modifier la classe", "class_name": "Nom de la classe", "class_capacity": "Capacité de la classe", "assigned_teacher": "Enseignant assigné", "schedule": "Emploi du temps"}, "attendance": {"title": "Gestion des présences", "mark_attendance": "Marquer la présence", "attendance_report": "Rapport de présence", "present": "Présent", "absent": "Absent", "late": "En retard", "excused": "Excusé"}, "grades": {"title": "Gestion des notes", "add_grade": "Ajouter une note", "edit_grade": "Modifier la note", "grade_book": "Carnet de notes", "subject": "<PERSON><PERSON>", "exam_type": "Type d'examen", "score": "Score"}}}, "teacher-dashboard": {"pages": {"dashboard": {"title": "<PERSON><PERSON> <PERSON> b<PERSON> En<PERSON>gnant", "welcome": "Bienvenue, {name}", "overview": "Vue d'ensemble", "my_classes": "Mes classes", "today_schedule": "Emploi du temps d'aujourd'hui", "recent_activities": "Activités récentes", "quick_actions": "Actions rapides", "students_count": "Nombre d'étudiants", "subjects_taught": "Matières enseignées", "upcoming_classes": "Cours à venir"}, "classes": {"title": "Mes classes", "class_details": "Détails de la classe", "student_list": "Liste des étudiants", "class_schedule": "Emploi du temps de la classe", "class_performance": "Performance de la classe"}, "attendance": {"title": "Présences", "take_attendance": "<PERSON><PERSON><PERSON> les présences", "attendance_history": "Historique des présences", "mark_present": "Marquer présent", "mark_absent": "<PERSON><PERSON> absent", "attendance_summary": "Résumé des présences"}, "grades": {"title": "Notes", "enter_grades": "Sai<PERSON> les notes", "grade_history": "Historique des notes", "grade_statistics": "Statistiques des notes", "class_average": "Moyenne de la classe", "student_progress": "Progrès de l'étudiant"}, "resources": {"title": "Ressources", "upload_resource": "<PERSON><PERSON><PERSON><PERSON>r une ressource", "my_resources": "<PERSON><PERSON> ressources", "shared_resources": "Ressources partagées", "resource_library": "Bibliothèque de ressources"}}}}, "components": {"chart": {"user_growth_trend": "Tendance de croissance des utilisateurs", "loading_user_stats": "Chargement des statistiques utilisateur...", "failed_to_load_data": "Échec du chargement des données", "select_year": "Sélectionner l'année"}, "performance_table": {"title": "Performance des écoles", "school_name": "Nom de l'école", "metric": "Métrique", "value": "<PERSON><PERSON>", "search_placeholder": "Rechercher une école...", "items_per_page": "Éléments par page", "showing": "Affichage de", "to": "à", "of": "sur", "entries": "entrées", "previous": "Précédent", "next": "Suivant", "no_data": "<PERSON><PERSON><PERSON> donnée disponible", "select_metric": "Sélectionner une métrique"}, "data_table": {"search": "Rechercher...", "filter": "<PERSON><PERSON><PERSON>", "export": "Exporter", "bulk_actions": "Actions en lot", "select_all": "<PERSON><PERSON>", "deselect_all": "<PERSON><PERSON>", "delete_selected": "Supprimer la sélection", "no_results": "Aucun résultat trouvé", "loading": "Chargement...", "rows_per_page": "Lignes par page", "page": "Page", "of_pages": "sur {total} pages", "no_data": "<PERSON><PERSON><PERSON> donnée disponible", "delete_selected_one": "Supprimer la sélection (1)", "delete_selected_count": "Supprimer la sélection ({count})", "view_details": "Voir les détails", "selected_count": "{selected} sur {total} sélectionnés", "select_all_count": "<PERSON><PERSON> s<PERSON> ({count})", "delete_all_count": "Tout supprimer ({count})", "active_filters": "Filtres actifs", "page_of": "Page {current} sur {total}", "items_per_page": "Éléments par page", "actions": "Actions", "all_items": "Tous les {type}"}, "modals": {"create": "<PERSON><PERSON><PERSON>", "edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>", "confirm": "Confirmer", "cancel": "Annuler", "save": "Enregistrer", "close": "<PERSON><PERSON><PERSON>", "are_you_sure": "Êtes-vous sûr ?", "this_action_cannot_be_undone": "Cette action ne peut pas être annulée", "delete_confirmation": "Êtes-vous sûr de vouloir supprimer cet élément ?", "delete_confirmation_with_name": "Êtes-vous sûr de vouloir supprimer <strong class=\"font-semibold text-pink-500\">{name}?</strong>", "delete_confirmation_prefix": "Êtes-vous sûr de vouloir supprimer", "bulk_delete_confirmation": "Êtes-vous sûr de vouloir supprimer {count} éléments sélectionnés ?", "delete_all_items": "Supprimer tous les {type}", "delete_selected_items": "Supprimer les {type} sélectionnés", "delete_all_warning": "Ceci supprimera définitivement TOUS les {type} du système. Cette action ne peut pas être annulée !", "delete_selected_warning": "Ceci supprimera définitivement les {type} sélectionnés. Cette action ne peut pas être annulée !", "danger_delete_all": "⚠️ DANGER : Opération de suppression totale", "bulk_delete_operation": "⚠️ Opération de suppression en lot", "about_to_delete_prefix": "Vous êtes sur le point de supprimer", "delete_all_system_warning": "Ceci supprimera TOUS les {type} du système et ne peut pas être annulé !", "enter_password_to_confirm": "Veuillez entrer votre mot de passe pour confirmer cette opération {type} :", "destructive": "destructrice", "bulk": "en lot", "delete_count_items": "Supprimer {count} {type}"}}, "notifications": {"title": "Notifications", "mark_as_read": "Marquer comme lu", "mark_all_read": "Tout marquer comme lu", "no_notifications": "Aucune notification", "new_notification": "Nouvelle notification", "notification_settings": "Paramètres de notification", "email_notifications": "Notifications par email", "push_notifications": "Notifications push"}, "forms": {"validation": {"required_field": "Ce champ est requis", "invalid_email": "<PERSON><PERSON><PERSON> email invalide", "invalid_phone": "Numéro de téléphone invalide", "password_too_short": "Le mot de passe doit contenir au moins 8 caractères", "passwords_dont_match": "Les mots de passe ne correspondent pas", "invalid_format": "Format invalide", "select_at_least_one": "Veuillez sélectionner au moins un élément à supprimer", "no_items_to_delete": "Aucun élément à supprimer", "password_required": "Veuillez entrer votre mot de passe pour confirmer la suppression", "errors": "Erreurs de validation", "fill_required_fields": "Veu<PERSON>z remplir tous les champs obligatoires.", "all_fields_required": "Tous les champs sont obligatoires."}, "placeholders": {"enter_name": "Entrez le nom", "enter_email": "Entrez l'email", "enter_phone": "Entrez le téléphone", "enter_address": "Entrez l'adresse", "select_option": "Sélectionnez une option", "search_placeholder": "Rechercher...", "password": "Mot de passe", "confirm_password": "Confirmer le mot de passe", "full_name": "Nom complet", "role": "R<PERSON><PERSON>", "select_role": "Sélectionner un rôle", "select_school": "Sélectionner une école", "website": "Site web", "principal_name": "Nom du directeur", "established_year": "Année de <PERSON>réation", "password_confirm_delete": "Tapez votre mot de passe pour confirmer la suppression"}}, "messages": {"success": {"saved": "Enregistré avec succès", "deleted": "Supprimé avec succès", "updated": "Mis à jour avec succès", "created": "<PERSON><PERSON><PERSON> avec succès", "sent": "<PERSON><PERSON><PERSON> a<PERSON> su<PERSON>", "all_items_deleted": "Tous les {type} ont été supprimés avec succès", "items_deleted": "{count} {type} supprimé(s) avec succès", "bulk_delete": "Suppression en lot réussie !", "transaction_saved": "Transaction enregistrée avec succès !", "class_created": "Classe créée avec succès !", "level_created": "Niveau créé avec succès !", "class_deleted": "Classe supprimée avec succès !", "level_deleted": "Niveau de classe supprimé avec succès !", "student_deleted": "Étudiant supprimé avec succès !"}, "error": {"generic": "Une erreur s'est produite", "network": "<PERSON><PERSON><PERSON> <PERSON>", "unauthorized": "Non autorisé", "forbidden": "Accès interdit", "not_found": "Non trouvé", "server_error": "<PERSON><PERSON><PERSON> du <PERSON>", "invalid_password": "Mot de passe invalide !", "bulk_delete_failed": "La suppression en lot a échoué. Veuillez réessayer.", "loading_data": "Erreur lors du chargement des données", "loading": "Erreur de chargement", "transaction_save_failed": "Erreur lors de l'enregistrement de la transaction. Veuillez réessayer.", "class_creation_failed": "Une erreur inconnue s'est produite lors de la création de la classe.", "level_creation_failed": "Une erreur inconnue s'est produite lors de la création du niveau de classe.", "class_deletion_failed": "Une erreur inconnue s'est produite lors de la suppression de cette classe.", "level_deletion_failed": "Une erreur inconnue s'est produite lors de la suppression de ce niveau de classe.", "student_deletion_failed": "Une erreur inconnue s'est produite lors de la suppression de l'étudiant.", "import_failed": "Échec de l'importation."}, "confirmation": {"delete": "Êtes-vous sûr de vouloir supprimer cet élément ?", "cancel": "Êtes-vous sûr de vouloir annuler ?", "logout": "Êtes-vous sûr de vouloir vous déconnecter ?"}, "info": {"please_wait": "Veuillez patienter pendant le traitement."}}, "registration": {"title": "Inscription d'Étudiant", "confirming": "Confirmation...", "confirm_payment_register": "Confirmer le Paiement et Inscrire", "steps": {"personal_info": "Informations Personnelles", "personal_info_desc": "Entrez les détails de l'étudiant.", "contact_info": "Informations de Contact", "contact_info_desc": "Fournissez les informations de contact de l'étudiant.", "guardian_details": "<PERSON><PERSON><PERSON> du Parent/<PERSON><PERSON><PERSON>", "guardian_details_desc": "Fournissez les informations du parent/tuteur.", "academic_info": "Informations Académiques", "academic_info_desc": "Détails concernant le parcours académique de l'étudiant.", "emergency_contact": "Contact d'Urgence", "emergency_contact_desc": "<PERSON><PERSON><PERSON>z les informations de contact d'urgence.", "medical_info": "Informations Médicales", "medical_info_desc": "Remplissez tout antécédent médical si applicable.", "consent": "Consentement et Déclaration", "consent_desc": "Acceptez les termes et conditions.", "fee_info": "Informations sur les Frais", "fee_info_desc": "Choisissez la structure des frais et les options de paiement.", "payment_confirmation": "Confirmation de Paiement", "payment_confirmation_desc": "Fournis<PERSON>z une preuve de paiement et confirmation d'inscription."}, "fields": {"student_address": "Adresse de l'Étudiant", "student_phone_optional": "Numéro de Téléphone de l'Étudiant (Optionnel)", "select_class": "Sélectionner une Classe", "previous_school": "École/Institution Précédente", "dob": "Date de Naissance", "search_student": "Rechercher un Étudiant", "search_placeholder": "<PERSON><PERSON><PERSON> le nom ou l'ID de l'étudiant...", "first_name": "Prénom", "last_name": "Nom de Famille", "middle_name": "Deuxième Prénom", "date_of_birth": "Date de Naissance", "nationality": "Nationalité", "male": "<PERSON><PERSON><PERSON><PERSON>", "female": "Fé<PERSON>n", "select_gender": "Sélectionner le genre", "place_of_birth": "<PERSON><PERSON> de Naissance", "search_guardian": "Rechercher un Tuteur dans le système", "guardian_search_placeholder": "<PERSON><PERSON><PERSON> le nom ou l'email...", "guardian_name": "Nom du Parent/Tuteur", "relationship": "Relation avec l'Étudiant", "mother": "<PERSON><PERSON>", "father": "Père", "brother": "<PERSON><PERSON>", "sister": "<PERSON>œur", "aunty": "<PERSON><PERSON>", "uncle": "Oncle", "grandmother": "Grand-Mère", "grandfather": "Grand-Père", "other": "<PERSON><PERSON>", "select_relationship": "Sélectionner la Relation", "same_address": "<PERSON>ême adresse que l'étudiant", "guardian_phone_required": "Numéro de Téléphone du Tuteur (Obligatoire)", "occupation": "Profession", "transcript_provided": "Cochez la case si des copies de relevés de notes ou bulletins ont été fournies", "same_as_guardian": "Même que les Informations du Tuteur", "health_condition": "État de Santé (Écrivez des notes si l'étudiant a un problème de santé)", "doctor_name": "Nom du Médecin", "doctor_phone_optional": "Numéro de Téléphone du Médecin (Optionnel)", "doctor_phone": "Téléphone du Médecin", "emergency_contact_name": "Nom du Contact d'Urgence", "emergency_contact_phone": "Téléphone du Contact d'Urgence"}, "fees": {"payment_option": "Option de Paiement", "full_payment": "<PERSON><PERSON><PERSON>t", "pay_installments": "Payer par Versements", "apply_scholarship": "Appliquer une Bourse", "scholarship_percentage": "Bourse (%)", "enter_percentage": "Entrez le pourcentage (ex: 20)", "scholarship_applied": "Bourse Appliquée", "total": "Total", "number_installments": "Nombre de Versements", "installment": "Versement", "installments": "Versements", "choose_due_dates": "Choisir les Dates d'Échéance", "installment_amounts": "Montants des Versements", "first_installment_now": "Premier Verse<PERSON> à Payer Maintenant", "select_applicable_fees": "Sélectionner les Frais Applicables", "no_fees_available": "Aucun frais applicable disponible"}, "resources": {"other_school_resources": "Autres Ressources Scolaires", "search_placeholder": "Rechercher des ressources...", "no_resources_found": "<PERSON><PERSON><PERSON> ressource trouvée"}, "summary": {"review_title": "Examiner le Résumé de Soumission", "student_info": "Informations de l'Étudiant", "guardian_info": "Informations du Tuteur", "emergency_contact": "Contact d'Urgence", "medical_info": "Informations Médicales", "none_reported": "Aucun signalé", "fees_resources": "Frais et Ressources", "total_payable": "Total à Payer", "payment_mode": "Mode de Paiement", "payment_dates": "Dates de Paiement", "installment_breakdown": "Répartition des Versements", "consent": "Consentement", "guardian_agreement": "Accord du Tu<PERSON>ur", "agreed": "Accepté", "not_agreed": "Non Accepté", "final_warning": "<PERSON><PERSON><PERSON><PERSON> vous assurer que toutes les informations sont correctes avant de soumettre. Vous ne pourrez pas les modifier par la suite."}, "messages": {"registration_failed": "L'inscription a échoué. Veuillez réessayer.", "registration_successful": "Inscription réussie !", "student_already_exists": "Un étudiant avec le même nom et la même date de naissance existe déjà dans cette école."}, "consent": {"title": "Déclaration de Consentement", "guardian_confirmation": "Le parent ou tuteur présent confirme qu'il est le tuteur légal de l'étudiant nommé dans cette demande. Il affirme que toutes les informations fournies sont vraies et exactes au mieux de sa connaissance.", "understand_accept": "Il comprend et accepte que", "data_processing": "L'école peut collecter, stocker et traiter les données personnelles et étudiantes conformément aux lois sur la protection des données.", "financial_responsibility": "Il est financièrement responsable des frais et accepte le mode de paiement sélectionné (complet ou par versements).", "installment_agreement": "S'il paie par versements, il accepte de payer aux dates d'échéance spécifiées. Le non-respect peut entraîner des pénalités ou des restrictions d'accès.", "policy_amendments": "L'école peut modifier les horaires, frais ou politiques avec un préavis approprié.", "emergency_treatment": "L'école peut chercher un traitement médical d'urgence si le tuteur ne peut être joint.", "school_policies": "Il a lu et accepte de suivre les politiques académiques, comportementales et de sécurité de l'école.", "admin_confirmation": "L'administrateur confirme ci-dessous que le parent/tuteur a examiné cette déclaration et donné son consentement verbal.", "verbal_consent_confirmation": "Je confirme que le parent/tuteur présent a donné son consentement verbal à la déclaration ci-dessus."}}, "language": {"french": "Français", "english": "<PERSON><PERSON><PERSON>", "select_language": "Sélectionner la langue", "language_changed": "Langue changée avec succès"}}