// components/ProfileSettings.tsx
"use client";

import React from "react";
import { User, Check } from "lucide-react"; // Ensure these are imported
import { Input } from "@/components/ui/Input";
import { Button } from "@/components/ui/Button";
import { UserSchema } from "@/app/models/UserModel"; // Ensure this path is correct
import { useTranslation } from "@/hooks/useTranslation";

interface ProfileSettingsProps {
  user: UserSchema | undefined;
  profileForm: {
    name: string;
    phone: string;
    address: string;
    avatar: string;
  };
  setProfileForm: React.Dispatch<React.SetStateAction<{
    name: string;
    phone: string;
    address: string;
    avatar: string;
  }>>;
  avatarPreviewUrl: string | null;
  handleAvatarChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleProfileSubmit: () => Promise<void>;
}

const ProfileSettings: React.FC<ProfileSettingsProps> = ({
  user,
  profileForm,
  setProfileForm,
  avatarPreviewUrl,
  handleAvatarChange,
  handleProfileSubmit,
}) => {
  const { t, tDashboard } = useTranslation();
  console.log("user in profile settings:", user);

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-semibold text-foreground mb-4">{tDashboard('super-admin', 'settings', 'profile_title')}</h2>

      {/* Avatar Section */}
      <div className="flex items-center space-x-4 mb-6">
        <img
          src={avatarPreviewUrl || "https://img.freepik.com/free-psd/3d-illustration-person-with-sunglasses_23-2149436188.jpg"}
          alt="User Avatar"
          className="w-24 h-24 rounded-full object-cover border-2 border-teal shadow-md"
          onError={(e) => {
            e.currentTarget.src = "https://placehold.co/100x100/aabbcc/ffffff?text=Avatar";
            e.currentTarget.onerror = null; // Prevent infinite loop
          }}
        />
        <div>
          <label htmlFor="avatar-upload" className="block text-sm font-medium text-foreground mb-1">{tDashboard('super-admin', 'settings', 'change_avatar')}</label>
          <Input
            id="avatar-upload"
            type="file"
            accept="image/*"
            onChange={handleAvatarChange}
            className="block w-full text-sm text-foreground file:mr-4 file:py-2 file:px-4
              file:rounded-full file:border-0 file:text-sm file:font-semibold
              file:bg-teal file:text-white hover:file:opacity-90 cursor-pointer"
          />
        </div>
      </div>

      {/* Grid for profile inputs */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label htmlFor="user-id-input" className="block text-sm  font-medium text-foreground mb-1">{t('common.user_id')}</label>
          <Input
            id="user-id-input"
            type="text"
            value={user?.user_id ?? ""}
            readOnly
            className="bg-background border-gray-300 rounded-md text-gray-400 focus:ring-teal focus:border-teal"
            placeholder={t('common.user_id')}
          />
        </div>

        <div>
          <label htmlFor="email-input" className="block text-sm font-medium text-foreground mb-1">{t('common.email')}</label>
          <Input
            id="email-input"
            type="email"
            value={user?.email ?? ""}
            readOnly
            className="bg-background border-gray-300 text-gray-400 rounded-md focus:ring-teal focus:border-teal"
            placeholder={t('common.email')}
          />
        </div>
        <div>
          <label htmlFor="role-input" className="block text-sm font-medium text-foreground mb-1">{t('common.role')}</label>
          <Input
            id="role-input"
            type="text"
            value={user?.role ?? ""}
            readOnly
            className="bg-background border-gray-300 text-gray-400 rounded-md focus:ring-teal focus:border-teal"
            placeholder={t('common.role')}
          />
        </div>
        <div>
          <label htmlFor="name-input" className="block text-sm font-medium text-foreground mb-1">{t('common.full_name')}</label>
          <Input
            id="name-input"
            type="text"
            value={profileForm.name}
            onChange={(e: { target: { value: any; }; }) =>
              setProfileForm({ ...profileForm, name: e.target.value })
            }
            className="bg-background border-gray-300 rounded-md focus:ring-teal focus:border-teal"
            placeholder={t('common.full_name')}
          />
        </div>
        <div>
          <label htmlFor="phone-input" className="block text-sm font-medium text-foreground mb-1">{t('common.phone_number')}</label>
          <Input
            id="phone-input"
            type="tel"
            value={profileForm.phone}
            onChange={(e: { target: { value: any; }; }) =>
              setProfileForm({ ...profileForm, phone: e.target.value })
            }
            className="bg-background border-gray-300 rounded-md focus:ring-teal focus:border-teal"
            placeholder={t('common.phone_number')}
          />
        </div>
        <div>
          <label htmlFor="address-input" className="block text-sm font-medium text-foreground mb-1">{t('common.address')}</label>
          <Input
            id="address-input"
            type="text"
            value={profileForm.address}
            onChange={(e: { target: { value: any; }; }) =>
              setProfileForm({ ...profileForm, address: e.target.value })
            }
            className="bg-background border-gray-300 rounded-md focus:ring-teal focus:border-teal"
            placeholder={t('common.address')}
          />
        </div>
      </div>
      <Button
        onClick={handleProfileSubmit}
        className="w-full py-3 bg-teal hover:opacity-90 text-white font-semibold rounded-md shadow-sm transition-colors duration-200 flex items-center justify-center space-x-2"
      >
        <Check className="h-5 w-5" /> {/* Save/Check Icon */}
        <span>{tDashboard('super-admin', 'settings', 'update_profile')}</span>
      </Button>
    </div>
  );
};

export default ProfileSettings;