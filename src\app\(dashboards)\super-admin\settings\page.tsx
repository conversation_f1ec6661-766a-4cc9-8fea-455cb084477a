"use client";

import React, { useEffect, useState } from "react";
import SuperLayout from "@/components/Dashboard/Layouts/SuperLayout";
import { Settings, User, CreditCard, SlidersHorizontal } from "lucide-react";
import { getUserById, updateUser, uploadUserAvatar } from "../../../services/UserServices";
import {
    getSettings,
    updateCreditSettings,
    updateGeneralSettings,
} from "../../../services/Settings";
import NotificationCard from "@/components/NotificationCard";
import useAuth from "@/app/hooks/useAuth";
import { UserSchema } from "@/app/models/UserModel";
import { SettingsSchema } from "@/app/models/Settings";

import ProfileSettings from "./components/ProfileSettings";
import CreditSettings from "./components/CreditSettings";
import GeneralSettings from "./components/GeneralSettings";

interface GeneralSettingsForm {
    platform_name: string;
    support_email: string;
    default_language: string;
    maintenance_mode: boolean;
    maintenance_message?: string; // <- optional
}

const SettingsPage: React.FC = () => {
    const BASE_URL = "/super-admin";
    const navigation = {
        icon: Settings,
        baseHref: `${BASE_URL}/settings`,
        title: "Settings",
    };

    const [user, setUser] = useState<UserSchema | undefined>(undefined);
    const [settings, setSettings] = useState<SettingsSchema | undefined>(undefined);
    const [isLoading, setIsLoading] = useState(true);

    const [isNotificationCard, setIsNotificationCard] = useState(false);
    const [notificationMessage, setNotificationMessage] = useState("");
    const [notificationType, setNotificationType] = useState<"success" | "error">("success");

    const [activeTab, setActiveTab] = useState<"profile" | "credit" | "general">("profile");

    const [profileForm, setProfileForm] = useState({
        name: "",
        phone: "",
        address: "",
        avatar: "",
    });

    const [newAvatarFile, setNewAvatarFile] = useState<File | null>(null);
    const [avatarPreviewUrl, setAvatarPreviewUrl] = useState<string | null>(null);

    const [creditForm, setCreditForm] = useState({
        resell_price_per_credit: 0,
        buy_price_per_credit: 0,
    });

    const [generalForm, setGeneralForm] = useState<GeneralSettingsForm>({
        platform_name: "",
        support_email: "",
        default_language: "en",
        maintenance_mode: false,
        // leave out maintenance_message so it's undefined by default
    });


    const { user: authUser } = useAuth();

    const setNotification = (message: string, type: "success" | "error") => {
        setNotificationMessage(message);
        setNotificationType(type);
        setIsNotificationCard(true);
    };

    useEffect(() => {
        const fetchData = async () => {
            try {
                if (!authUser || !authUser._id) {
                    throw new Error("User not authenticated");
                }

                const fetchedUser = await getUserById(authUser.user_id as string);
                const fetchedSettings = await getSettings();

                setUser(fetchedUser);
                setSettings(fetchedSettings);
                
                setProfileForm({
                    name: fetchedUser.name || "",
                    phone: fetchedUser.phone || "",
                    address: fetchedUser.address || "",
                    avatar: fetchedUser.avatar || "https://img.freepik.com/free-psd/3d-illustration-person-with-sunglasses_23-2149436188.jpg",
                });

                setAvatarPreviewUrl(
                    fetchedUser.avatar || "https://img.freepik.com/free-psd/3d-illustration-person-with-sunglasses_23-2149436188.jpg"
                );

                setCreditForm({
                    resell_price_per_credit: fetchedSettings.credit?.resell_price_per_credit || 0,
                    buy_price_per_credit: fetchedSettings.credit?.buy_price_per_credit || 0,
                });

                setGeneralForm({
                    platform_name: fetchedSettings.general?.platform_name || "",
                    support_email: fetchedSettings.general?.support_email || "",
                    default_language: fetchedSettings.general?.default_language || "en",
                    maintenance_mode: fetchedSettings.general?.maintenance_mode || false,
                    maintenance_message: fetchedSettings.general?.maintenance_message || "",
                });

                setIsLoading(false);
            } catch (error) {
                console.error("Error fetching data:", error);
                setNotification("Failed to load settings", "error");
            }
        };

        fetchData();
    }, [authUser]);

    const handleAvatarChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
        const file = e.target.files[0];
        if (avatarPreviewUrl?.startsWith("blob:")) {
            URL.revokeObjectURL(avatarPreviewUrl);
        }
        setNewAvatarFile(file);
        setAvatarPreviewUrl(URL.createObjectURL(file));
    }
    };

    const handleProfileSubmit = async () => {
        try {
            if (!user || !user._id) {
                throw new Error("User not loaded");
            }

            let avatarToUpdate = profileForm.avatar;
            if (newAvatarFile) {
                const uploadResult = await uploadUserAvatar(user.user_id, newAvatarFile);
                avatarToUpdate = uploadResult.avatarUrl;
                setAvatarPreviewUrl(uploadResult.avatarUrl);

            }

            await updateUser(user.user_id, {
                name: profileForm.name,
                user_id: user.user_id,
                role: user.role,
                phone: profileForm.phone,
                address: profileForm.address,
                avatar: avatarToUpdate,
            });

            setUser((prevUser) => ({
                ...prevUser!,
                name: profileForm.name,
                phone: profileForm.phone,
                address: profileForm.address,
                avatar: avatarToUpdate,
            }));
            setProfileForm((prevForm) => ({ ...prevForm}));

            setNotification("Profile updated successfully", "success");
        } catch (error) {
            console.error("Error updating profile:", error);
            setNotification("Failed to update profile", "error");
        }
    };

    const handleCreditSubmit = async () => {
        try {
            if (!settings || !settings._id) {
                throw new Error("Settings not loaded");
            }

            const updatedCredit = {
                resell_price_per_credit: creditForm.resell_price_per_credit,
                buy_price_per_credit: creditForm.buy_price_per_credit,
            };

            await updateCreditSettings(updatedCredit);
            setNotification("Credit settings updated successfully", "success");
        } catch (error) {
            console.error("Error updating credit settings:", error);
            setNotification("Failed to update credit settings", "error");
        }
    };

    const handleGeneralSubmit = async () => {
        try {
            if (!settings || !settings._id) {
                throw new Error("Settings not loaded");
            }

            const updatedGeneral = {
                platform_name: generalForm.platform_name,
                support_email: generalForm.support_email,
                default_language: generalForm.default_language,
                maintenance_mode: generalForm.maintenance_mode,
                maintenance_message: generalForm.maintenance_mode
                    ? generalForm.maintenance_message
                    : undefined,
            };

            await updateGeneralSettings(updatedGeneral);
            setNotification("General settings updated successfully", "success");
        } catch (error) {
            console.error("Error updating general settings:", error);
            setNotification("Failed to update general settings", "error");
        }
    };

    if (isLoading) return <div className="p-6 text-foreground">Loading settings...</div>;

    return (
        <SuperLayout
            navigation={navigation}
            showGoPro={true}
            onLogout={() => console.log("Logged out")}
        >
            {isNotificationCard && (
                <NotificationCard
                    title="Notification"
                    icon={
                        notificationType === "success" ? (
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                <path
                                    d="M12 22C17.5 22 22 17.5 22 12C22 6.5 17.5 2 12 2C6.5 2 2 6.5 2 12C2 17.5 6.5 22 12 22Z"
                                    stroke="#15803d"
                                    strokeWidth="1.5"
                                />
                                <path
                                    d="M7.75 11.9999L10.58 14.8299L16.25 9.16992"
                                    stroke="#15803d"
                                    strokeWidth="1.5"
                                />
                            </svg>
                        ) : (
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                <circle cx="12" cy="12" r="10" stroke="#dc2626" strokeWidth="2" />
                                <path
                                    d="M8 8L16 16M16 8L8 16"
                                    stroke="#dc2626"
                                    strokeWidth="2"
                                    strokeLinecap="round"
                                />
                            </svg>
                        )
                    }
                    message={notificationMessage}
                    onClose={() => setIsNotificationCard(false)}
                    type={notificationType}
                    isVisible={isNotificationCard}
                    isFixed={true}
                />
            )}

            <div className="flex flex-col md:flex-row gap-8 p-6 w-full max-w-6xl mx-auto ">
                <div className="flex flex-col space-y-2 w-full md:w-1/4 ">
                    <button
                        className={`w-full text-left px-4 py-3 rounded-lg transition-colors duration-200 flex items-center space-x-2 ${activeTab === "profile"
                            ? "bg-teal text-white shadow-md"
                            : "bg-background text-foreground hover:bg-background-darker"
                            }`}
                        onClick={() => setActiveTab("profile")}
                    >
                        <User className="h-5 w-5" />
                        <span>Profile Settings</span>
                    </button>
                    <button
                        className={`w-full text-left px-4 py-3 rounded-lg transition-colors duration-200 flex items-center space-x-2 ${activeTab === "credit"
                            ? "bg-teal text-white shadow-md"
                            : "bg-background text-foreground hover:bg-background-darker"
                            }`}
                        onClick={() => setActiveTab("credit")}
                    >
                        <CreditCard className="h-5 w-5" />
                        <span>Credit Settings</span>
                    </button>
                    <button
                        className={`w-full text-left px-4 py-3 rounded-lg transition-colors duration-200 flex items-center space-x-2 ${activeTab === "general"
                            ? "bg-teal text-white shadow-md"
                            : "bg-background text-foreground hover:bg-background-darker"
                            }`}
                        onClick={() => setActiveTab("general")}
                    >
                        <SlidersHorizontal className="h-5 w-5" />
                        <span>General Settings</span>
                    </button>
                </div>

                <div className="flex-1 w-full md:w-3/4 bg-gray-50 dark:bg-gray-800  p-6 rounded-lg shadow-md">
                    {activeTab === "profile" && (
                        <ProfileSettings
                            user={user}
                            profileForm={profileForm}
                            setProfileForm={setProfileForm}
                            avatarPreviewUrl={avatarPreviewUrl}
                            handleAvatarChange={handleAvatarChange}
                            handleProfileSubmit={handleProfileSubmit}
                        />
                    )}

                    {activeTab === "credit" && (
                        <CreditSettings
                            creditForm={creditForm}
                            setCreditForm={setCreditForm}
                            handleCreditSubmit={handleCreditSubmit}
                        />
                    )}

                    {activeTab === "general" && (
                        <GeneralSettings
                            generalForm={generalForm}
                            setGeneralForm={setGeneralForm}
                            handleGeneralSubmit={handleGeneralSubmit}
                        />
                    )}
                </div>
            </div>
        </SuperLayout>
    );
};

export default SettingsPage;
