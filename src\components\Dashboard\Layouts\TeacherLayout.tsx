"use client";
import React, { useState, useEffect, useMemo } from "react";
import {
  LayoutDashboard,
  BookOpen,
  ClipboardList,
  Settings,
  GraduationCap,
  School,
  Menu,
  X,
  FileCheck2,
  Percent,
  Clock4,
  Megaphone,
  DollarSign,
  CreditCard,
  Receipt,
  FileText,
  Calendar
} from "lucide-react";
import Divider from "../../widgets/Divider";
import SearchBox from "../../widgets/SearchBox";
import SidebarButton from "../SideNavButton";
import SidebarGroup from "../SidebarGroup";
import Avatar from "../Avatar";
import Logo from "../../widgets/Logo";
import NavigationBar from "../NavigationBar";
import useAuth from "@/app/hooks/useAuth";
import ProtectedRoute from "@/components/utils/ProtectedRoute";
import Breadcrumbs from "../BreadCrums";
import { getTeacherPermissions, TeacherPermissions } from "@/app/services/TeacherPermissionServices";

interface TeacherLayoutProps {
  navigation: {
    icon: React.ElementType;
    baseHref: string;
    title: string;
  };
  selectedSchool?: {
    _id: string;
    name: string;
  } | null;
  onSchoolChange?: () => void;
  onLogout: () => void;
  children: React.ReactNode;
}

const TeacherLayout: React.FC<TeacherLayoutProps> = ({
  navigation,
  selectedSchool,
  onSchoolChange,
  onLogout,
  children,
}) => {
  const { user } = useAuth();
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [permissions, setPermissions] = useState<TeacherPermissions | null>(null);
  const [loadingPermissions, setLoadingPermissions] = useState(false);

  // Load teacher permissions when school changes
  useEffect(() => {
    const loadPermissions = async () => {
      if (!selectedSchool?._id) {
        setPermissions(null);
        return;
      }

      setLoadingPermissions(true);
      try {
        const teacherData = await getTeacherPermissions(selectedSchool._id);
        setPermissions(teacherData.permissions);
      } catch (error) {
        console.error('Error loading teacher permissions:', error);
        setPermissions(null);
      } finally {
        setLoadingPermissions(false);
      }
    };

    loadPermissions();
  }, [selectedSchool?._id]);

  const avatar = {
    avatarUrl: user?.avatar || "https://img.freepik.com/free-psd/3d-illustration-person-with-sunglasses_23-2149436188.jpg",
    name: user?.name || `${user?.first_name} ${user?.last_name}` || "Teacher",
    role: "Teacher",
  };

  const BASE_URL = "/teacher-dashboard";

  // Individual navigation items (not in groups)
  const individualNavItems = [
    { icon: LayoutDashboard, name: "Dashboard", href: `${BASE_URL}/dashboard` },
  ];

  // Helper function to check permissions
  const hasPermission = (module: keyof TeacherPermissions, permission: string): boolean => {
    if (!permissions) return false;
    return permissions[module] && (permissions[module] as any)[permission] === true;
  };

  // Generate navigation groups based on permissions (memoized to prevent infinite re-renders)
  const navigationGroups = useMemo(() => {
    if (!permissions) {
      // Return empty groups if no permissions loaded
      return [];
    }

    const groups = [];

    // My Classes group
    const myClassesItems = [];
    if (hasPermission('classes', 'view_all_classes')) {
      myClassesItems.push({ icon: BookOpen, name: "Classes", href: `${BASE_URL}/classes` });
    }
    if (hasPermission('students', 'view_all_students')) {
      myClassesItems.push({ icon: GraduationCap, name: "Students", href: `${BASE_URL}/students` });
    }
    if (hasPermission('classes', 'manage_class_schedules')) {
      myClassesItems.push({ icon: Clock4, name: "Schedule", href: `${BASE_URL}/timetable` });
    }
    if (hasPermission('classes', 'manage_class_schedules')) {
      myClassesItems.push({ icon: Calendar, name: "Exam Supervisions", href: `${BASE_URL}/supervisions` });
    }
    if (myClassesItems.length > 0) {
      groups.push({
        title: "My Classes",
        icon: BookOpen,
        items: myClassesItems
      });
    }

    // Academic Tasks group
    const academicTasksItems = [];
    if (hasPermission('academic_records', 'take_attendance_assigned_classes')) {
      academicTasksItems.push({ icon: FileCheck2, name: "Attendance", href: `${BASE_URL}/attendance` });
    }
    if (hasPermission('academic_records', 'view_grades_assigned_classes')) {
      academicTasksItems.push({ icon: Percent, name: "Grades", href: `${BASE_URL}/grades` });
    }
    if (academicTasksItems.length > 0) {
      groups.push({
        title: "Academic Tasks",
        icon: ClipboardList,
        items: academicTasksItems
      });
    }

    // Resources group
    const resourcesItems = [];
    if (hasPermission('resources', 'view_resources')) {
      resourcesItems.push({ icon: BookOpen, name: "Teaching Materials", href: `${BASE_URL}/resources` });
    }
    if (hasPermission('announcements', 'view_announcements')) {
      resourcesItems.push({ icon: Megaphone, name: "Announcements", href: `${BASE_URL}/announcements` });
    }
    if (resourcesItems.length > 0) {
      groups.push({
        title: "Resources",
        icon: Megaphone,
        items: resourcesItems
      });
    }

    // Financial group (if teacher has financial permissions)
    const financialItems = [];
    if (hasPermission('financials', 'view_student_fee_balances')) {
      financialItems.push({ icon: CreditCard, name: "Fee Balances", href: `${BASE_URL}/fees` });
    }
    if (hasPermission('financials', 'record_fee_payments')) {
      financialItems.push({ icon: Receipt, name: "Fee Payments", href: `${BASE_URL}/fee-payments` });
    }
    if (financialItems.length > 0) {
      groups.push({
        title: "Financial",
        icon: DollarSign,
        items: financialItems
      });
    }

    // Reports group
    const reportsItems = [];
    if (hasPermission('reports', 'generate_student_reports')) {
      reportsItems.push({ icon: FileText, name: "Student Reports", href: `${BASE_URL}/reports/students` });
    }
    if (hasPermission('reports', 'generate_attendance_reports')) {
      reportsItems.push({ icon: Calendar, name: "Attendance Reports", href: `${BASE_URL}/reports/attendance` });
    }
    if (reportsItems.length > 0) {
      groups.push({
        title: "Reports",
        icon: FileText,
        items: reportsItems
      });
    }

    return groups;
  }, [permissions]);

  const settingsLink = {
    icon: Settings,
    name: "Settings",
    href: `${BASE_URL}/settings`
  };

  return (
    <ProtectedRoute allowedRoles={["teacher"]}>
      <div className="flex h-screen overflow-hidden sm:p-4">
        {/* Mobile Sidebar Toggle */}
        <button
          className="md:hidden p-2 bg-foreground text-background rounded-lg fixed top-4 left-4 z-50"
          onClick={() => setIsSidebarOpen(!isSidebarOpen)}
        >
          {isSidebarOpen ? <X size={24} /> : <Menu size={24} />}
        </button>

        {/* Sidebar */}
        <div
          className={`flex w-[290px] flex-col border border-gray-300 dark:border dark:border-gray-800 h-full shadow-lg p-2 rounded-lg fixed inset-y-0 left-0 z-40 bg-widget transition-transform lg:relative lg:translate-x-0 ${
            isSidebarOpen ? "translate-x-0" : "-translate-x-full"
          }`}
        >
          <div className="flex flex-col gap-3 overflow-auto subtle-scrollbar">
            <div className="flex flex-col items-center gap-2 my-4">
              <Logo />
              <Divider />
            </div>

            {/* School Selector */}
            {selectedSchool && (
              <div className="px-2">
                <div className="bg-teal/10 border border-teal/20 rounded-lg p-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <School className="h-4 w-4 text-teal" />
                      <div>
                        <p className="text-xs text-foreground/60">Current School</p>
                        <p className="text-sm font-medium text-foreground truncate">
                          {selectedSchool.name}
                        </p>
                      </div>
                    </div>
                    {onSchoolChange && (
                      <button
                        onClick={onSchoolChange}
                        className="text-xs text-teal hover:text-teal-600 font-medium"
                      >
                        Change
                      </button>
                    )}
                  </div>
                </div>
              </div>
            )}

            <SearchBox />

            <div className="flex flex-col gap-1">
              {/* Individual Navigation Items */}
              {individualNavItems.map((item) => (
                <SidebarButton
                  key={item.name}
                  icon={item.icon}
                  name={item.name}
                  href={item.href}
                  disabled={!selectedSchool} // Disable navigation if no school selected
                />
              ))}

              {/* Divider */}
              <div className="my-2">
                <Divider />
              </div>

              {/* Grouped Navigation Items */}
              {loadingPermissions ? (
                <div className="flex items-center justify-center py-4">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-teal"></div>
                  <span className="ml-2 text-sm text-foreground/60">Loading permissions...</span>
                </div>
              ) : (
                navigationGroups.map((group) => (
                  <SidebarGroup
                    key={group.title}
                    title={group.title}
                    icon={group.icon}
                    items={group.items.map(item => ({
                      ...item,
                      disabled: !selectedSchool // Disable all items if no school selected
                    }))}
                    defaultExpanded={false} // Let the component handle expansion based on active routes
                  />
                ))
              )}
            </div>
          </div>

          {/* Footer */}
          <div className="mt-auto flex flex-col gap-3">
            <SidebarButton
              icon={settingsLink.icon}
              name={settingsLink.name}
              href={settingsLink.href}
            />

            {/* Quick School Change Button */}
            {selectedSchool && onSchoolChange && (
              <button
                onClick={onSchoolChange}
                className="flex items-center space-x-2 px-3 py-2 text-sm text-foreground/70 hover:text-foreground hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors"
              >
                <School className="h-4 w-4" />
                <span>Switch School</span>
              </button>
            )}

            <Divider />
            <Avatar
              avatarUrl={avatar.avatarUrl}
              name={avatar.name}
              role={avatar.role}
              onLogout={onLogout}
            />
          </div>
        </div>

        {/* Main Content */}
        <div className="sm:px-6 px-2 py-2 w-full flex flex-col gap-4 lg:w-[95%] overflow-auto custom-scrollbar">
          <div className="sticky top-0 z-20 flex items-center justify-between">
            <NavigationBar
              icon={navigation.icon}
              baseHref={navigation.baseHref}
              title={navigation.title}
              isSidebarOpen={isSidebarOpen}
              toggleSidebar={() => setIsSidebarOpen(!isSidebarOpen)}
              onLogout={onLogout}
            />
          </div>

          <div className="flex lg:hidden flex-col gap-2">
            <Breadcrumbs baseHref={navigation.baseHref} icon={navigation.icon} />
            <p className="text-2xl font-semibold text-foreground">{navigation.title}</p>
          </div>

          {/* No School Selected Warning */}
          {!selectedSchool && (
            <div className="bg-yellow-50 dark:bg-yellow-900/30 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
              <div className="flex items-center space-x-2">
                <School className="h-5 w-5 text-yellow-600" />
                <div>
                  <p className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                    No School Selected
                  </p>
                  <p className="text-sm text-yellow-700 dark:text-yellow-300">
                    Please select a school to access your dashboard features.
                  </p>
                </div>
              </div>
            </div>
          )}

          <div className="">{children}</div>
        </div>
      </div>
    </ProtectedRoute>
  );
};

export default TeacherLayout;