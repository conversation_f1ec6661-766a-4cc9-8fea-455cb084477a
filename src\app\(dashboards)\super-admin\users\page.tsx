"use client";

import SuperLayout from '@/components/Dashboard/Layouts/SuperLayout';
import CircularLoader from '@/components/widgets/CircularLoader';
import React, { Suspense, useEffect, useState } from 'react';
import { School, Users } from 'lucide-react';
import DataTable from '@/components/utils/DataTable';
import CreateUserModal from './components/CreateUserModal';
import { useRouter } from 'next/navigation';
import DeleteUserModal from './components/DeleteUserModal';
import { UserCreateSchema, UserSchema } from '@/app/models/UserModel';
import { createUser, deleteUser, getUsers, verifyPassword, deleteMultipleUsers, deleteAllUsers } from '@/app/services/UserServices';
import NotificationCard, { NotificationType } from '@/components/NotificationCard';
import BulkDeleteModal from '@/components/modals/BulkDeleteModal';
import { SchoolSchema } from '@/app/models/SchoolModel';
import { getSchools } from '@/app/services/SchoolServices';
import Link from 'next/link';
import useAuth from '@/app/hooks/useAuth';
import DataTableFix from '@/components/utils/TableFix';
import { motion } from 'framer-motion';
import ActionButton from '@/components/ActionButton';
import { useTranslation } from '@/hooks/useTranslation';

export default function Page() {
  const { t, tDashboard } = useTranslation();
  const BASE_URL = "/super-admin";

  const navigation = {
    icon: Users,
    baseHref: `${BASE_URL}/users`,
    title: t('navigation.users'),
  };

  function UserContent() {
    const router = useRouter();
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [selectedRole, setSelectedRole] = useState("All");
    const [selectedUsers, setSelectedUsers] = useState<UserSchema[]>([]);
    const [users, setUsers] = useState<UserSchema[]>([]);
    const [schools, setSchools] = useState<SchoolSchema[]>([]);
    const [userToDelete, setUserToDelete] = useState<UserSchema | null>(null);
    const [isDeleteUserModalOpen, setIsDeleteUserModalOpen] = useState(false);
    const [loadingData, setLoadingData] = useState(false);
    const [isNotificationCard, setIsNotificationCard] = useState(false);
    const [notificationMessage, setNotificationMessage] = useState("");
    const [notificationType, setNotificationType] = useState<NotificationType>("success")
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [submitStatus, setSubmitStatus] = useState<"success" | "failure" | null>(null);
    const { user } = useAuth();

    // Bulk delete modal states
    const [isBulkDeleteModalOpen, setIsBulkDeleteModalOpen] = useState(false);
    const [bulkDeleteType, setBulkDeleteType] = useState<"selected" | "all">("selected");
    const [selectedUserIds, setSelectedUserIds] = useState<string[]>([]);

    // Key to force DataTable re-render and clear selection
    const [tableKey, setTableKey] = useState(0);

    const fetchSchools = async () => {
      setLoadingData(true);
      try {
        const fetchedUsers = await getUsers();
        const fetchedSchools = await getSchools();
        setSchools(fetchedSchools);
        setUsers(fetchedUsers);
      } catch (error) {
        console.error("Error fetching schools or User:", error);
      } finally {
        setLoadingData(false);
      }
    };

    useEffect(() => {
      fetchSchools();
    }, []);

    // Create a mapping from school_id to school name
    const schoolNameMap: { [key: string]: string } = schools.reduce((map, school) => {
      map[school._id] = school.name;
      return map;
    }, {} as { [key: string]: string });

    const columns = [
      { header: "User ID", accessor: (row: UserSchema) => row.user_id },
      { header: t('common.name'), accessor: (row: UserSchema) => { return <Link href={`${BASE_URL}/users/view?id=${row.user_id}`}>{row.name}</Link>; } },
      { header: t('common.email'), accessor: (row: UserSchema) => row.email },
      { header: t('forms.placeholders.role'), accessor: (row: UserSchema) => row.role },
      {
        header: t('navigation.schools'),
        accessor: (row: UserSchema) =>
          (row.school_ids ?? []).map(id => schoolNameMap[id] || t('common.unknown')).join(", "),
      },
      {
        header: tDashboard('super-admin', 'users', 'last_login'),
        accessor: (row: UserSchema) => {
          const date = new Date(row.lastLogin || "No Date");
          return date.toLocaleString();
        }
      }
    ]
    const actions = [
      {
        label: "View",
        onClick: (user: UserSchema) => {
          router.push(`${BASE_URL}/users/view?id=${user.user_id}`);
        },
      },
      {
        label: "Delete",
        onClick: (user: UserSchema) => {
          setUserToDelete(user);
          setIsDeleteUserModalOpen(true);
        },
      },
    ]

    const uniqueRoles = Array.from(new Set(users.map(user => user.role))).sort();
    const roles = ["All", ...uniqueRoles];

    const filteredUsers = selectedRole === "All"
      ? users
      : users.filter(user => user.role === selectedRole);

    const handleSaveUser = async (userData: UserCreateSchema) => {
      setIsSubmitting(true);
      setSubmitStatus(null);
      setLoadingData(true);
      try {
        const newUser: UserCreateSchema = {
          name: userData.name,
          email: userData.email,
          role: userData.role,
          password: userData.password,
          phone: userData.phone,
          address: userData.address,
          school_ids: userData.school_ids,
          isVerified: userData.isVerified ?? false,
        };

        const data = await createUser(newUser);

        if (data) {
          fetchSchools();
          setSubmitStatus("success");
          setNotificationMessage("User created successfully!");
          setIsNotificationCard(true);
          setNotificationType("success");
          setTimeout(() => {
            setIsModalOpen(false);
            setSubmitStatus(null);
          }, 10000);
        }
      } catch (error) {
        console.error("Error creating user:", error);
        setSubmitStatus("failure");
        const errorMessage =
          error instanceof Error
            ? error.message
            : "An unknown error occurred while creating this user.";

        setNotificationMessage(errorMessage);
        setNotificationType("error");
        setIsNotificationCard(true);
      } finally {
        setLoadingData(false);
        setIsSubmitting(false);
      }
    };

    const handleDelete = async (password: string) => {
      setIsSubmitting(true);
      setSubmitStatus(null);
      const passwordVerified = user ? await verifyPassword(password, user.email) : false;

      if (!passwordVerified) {
        setNotificationMessage("Invalid Password!");
        setNotificationType("error");
        setIsNotificationCard(true);
        setIsSubmitting(false);
        setSubmitStatus("failure");
        setTimeout(() => {
          setUserToDelete(null);
          setSubmitStatus(null);
        }, 10000);
        return;
      }

      if (userToDelete) {
        try {
          await deleteUser(userToDelete.user_id);
          fetchSchools();
          setSubmitStatus("success");
          setNotificationMessage("User Deleted successfully!");
          setIsNotificationCard(true);
          setNotificationType("success");

          setTimeout(() => {
            setUserToDelete(null);
            setSubmitStatus(null);
          }, 10000);
        } catch (error) {
          console.error("Error Deleting User:", error);
          setSubmitStatus("failure");
          const errorMessage =
            error instanceof Error
              ? error.message
              : "An unknown error occurred while deleting the user.";

          setNotificationMessage(errorMessage);
          setNotificationType("error");
          setIsNotificationCard(true);
        } finally {
          setIsSubmitting(false);
        }
      }
    };

    const handleDeleteMultiple = async (selectedIds: string[]) => {
      if (selectedIds.length === 0) {
        alert(t('messages.validation.select_at_least_one'));
        return;
      }
      setSelectedUserIds(selectedIds);
      setBulkDeleteType("selected");
      setIsBulkDeleteModalOpen(true);
    };

    const handleDeleteAll = async () => {
      if (users.length === 0) {
        alert(t('messages.validation.no_items_to_delete'));
        return;
      }
      setBulkDeleteType("all");
      setIsBulkDeleteModalOpen(true);
    };

    const handleBulkDeleteConfirm = async (password: string) => {
      setIsSubmitting(true);
      setSubmitStatus(null);

      const passwordVerified = user ? await verifyPassword(password, user.email) : false;
      if (!passwordVerified) {
        setNotificationMessage(t('messages.error.invalid_password'));
        setNotificationType("error");
        setIsNotificationCard(true);
        setIsSubmitting(false);
        setSubmitStatus("failure");
        return;
      }

      try {
        if (bulkDeleteType === "all") {
          await deleteAllUsers();
          setNotificationMessage("All users deleted successfully!");
        } else {
          await deleteMultipleUsers(selectedUserIds);
          setNotificationMessage(`${selectedUserIds.length} user(s) deleted successfully!`);
        }

        setNotificationType("success");
        setIsNotificationCard(true);
        setSubmitStatus("success");

        if (bulkDeleteType === "all") {
          setUsers([]);
        } else {
          setUsers(prev => prev.filter(user => !selectedUserIds.includes(user._id)));
        }

        setSelectedUsers([]);
        setTableKey(prev => prev + 1);

        setTimeout(() => {
          setIsBulkDeleteModalOpen(false);
          setSubmitStatus(null);
        }, 2000);

      } catch (error) {
        console.error("Error in bulk deletion:", error);
        const errorMessage = error instanceof Error ? error.message : "Failed to delete users";
        setNotificationMessage(errorMessage);
        setNotificationType("error");
        setIsNotificationCard(true);
        setSubmitStatus("failure");
      } finally {
        setIsSubmitting(false);
      }
    };

    return (
      <div className="">
        {isNotificationCard && (
          <NotificationCard
            title="Notification"
            icon={
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 22C17.5 22 22 17.5 22 12C22 6.5 17.5 2 12 2C6.5 2 2 6.5 2 12C2 17.5 6.5 22 12 22Z" stroke="#15803d " strokeWidth="1.5" stroke-linecap="round" strokeLinejoin="round" />
                <path d="M7.75 11.9999L10.58 14.8299L16.25 9.16992" stroke="#15803d " strokeWidth="1.5" stroke-linecap="round" strokeLinejoin="round" />
              </svg>
            }
            message={notificationMessage}
            onClose={() => setIsNotificationCard(false)}
            type={notificationType}
            isVisible={isNotificationCard}
            isFixed={true}
          />
        )}
        <div className="flex flex-col mb-4"> {/* Changed to flex-col */}
          <div className="mb-4">
            <ActionButton
              action="add"
              label="Add New User"
              onClick={() => setIsModalOpen(true)}
            />
          </div>
          <p className='text-foreground mb-4'>Filter By Role</p>
          {/* Role Filter Tags below the button */}
          <div className="flex flex-wrap gap-2"> {/* Removed mb-4 here, as it's for button group */}
            {roles.map(role => (
              <motion.button
                key={role}
                onClick={() => setSelectedRole(role)}
                className={`
                  px-4 py-2 rounded-md text-sm font-medium border
                  ${selectedRole === role
                    ? 'bg-teal-600 text-foreground border-teal-600 shadow-md'
                    : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-100 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-700 dark:hover:bg-gray-700'
                  }
                  transition-all duration-200 ease-in-out
                `}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                {role === "All" ? "All Users" : role.charAt(0).toUpperCase() + role.slice(1)}
              </motion.button>
            ))}
          </div>
          {/* End Role Filter Tags */}

        </div>

        {isModalOpen && (
          <CreateUserModal
            onClose={() => { setIsModalOpen(false); setSubmitStatus(null); }}
            onSave={handleSaveUser}
            roles={roles.filter(role => role !== "All")}
            schools={schools}
            isSubmitting={isSubmitting}
            submitStatus={submitStatus}
          />
        )}
        {isDeleteUserModalOpen && userToDelete && (
          <DeleteUserModal
            userName={userToDelete.name}
            onClose={() => {
              setIsDeleteUserModalOpen(false);
              setUserToDelete(null);
            }}
            onDelete={handleDelete}
            isSubmitting={isSubmitting}
            submitStatus={submitStatus}
          />
        )}

        <DataTableFix
          key={tableKey}
          columns={columns}
          data={filteredUsers}
          actions={actions}
          defaultItemsPerPage={5}
          loading={loadingData}
          onLoadingChange={setLoadingData}
          onSelectionChange={setSelectedUsers}
          enableBulkActions={true}
          handleDeleteMultiple={handleDeleteMultiple}
          handleDeleteAll={handleDeleteAll}
          idAccessor="_id"
        />

        {isBulkDeleteModalOpen && (
          <BulkDeleteModal
            isOpen={isBulkDeleteModalOpen}
            onClose={() => {
              setIsBulkDeleteModalOpen(false);
              setSubmitStatus(null);
            }}
            onConfirm={handleBulkDeleteConfirm}
            title={bulkDeleteType === "all" ? "Delete All Users" : "Delete Selected Users"}
            message={
              bulkDeleteType === "all"
                ? `Are you sure you want to delete ALL ${users.length} users? This action cannot be undone.`
                : `Are you sure you want to delete ${selectedUserIds.length} selected user(s)? This action cannot be undone.`
            }
            itemCount={bulkDeleteType === "all" ? users.length : selectedUserIds.length}
            itemType="users"
            isDeleteAll={bulkDeleteType === "all"}
            isSubmitting={isSubmitting}
            submitStatus={submitStatus}
            requirePassword={true}
          />
        )}
      </div>
    );
  }

  return (
    <Suspense fallback={
      <div className="flex justify-center items-center h-screen absolute top-0 left-0 z-50">
        <CircularLoader size={32} color="teal" />
      </div>
    }>
      <SuperLayout
        navigation={navigation}
        showGoPro={true}
        onLogout={() => console.log("Logged out")}
      >
        <UserContent />
      </SuperLayout>
    </Suspense>
  );
}